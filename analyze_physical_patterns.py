#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物理分析脚本：深入分析太阳能发电的物理规律
"""

import sys
import numpy as np
import pandas as pd
import asyncio
from datetime import datetime
from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent

async def analyze_physical_patterns():
    print("🔬 深度物理分析：光伏发电季节性规律")
    print("=" * 60)
    
    # 获取数据
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    # 解析位置
    location = await geo_agent.parse_location("北京市万橡悦府2期")
    lat, lon = location.latitude, location.longitude
    
    # 获取太阳辐射数据
    solar_data = await solar_agent.get_solar_data(lat, lon)
    
    print(f"📍 位置: {lat:.3f}°N, {lon:.3f}°E")
    print(f"📊 数据天数: {len(solar_data)} 天")
    print()
    
    # 计算发电
    results = pv_agent.calculate_power_generation(
        solar_data=solar_data,
        system_capacity=10.0,
        tilt_angle=30.0,
        azimuth_angle=180.0
    )
    
    # 分析原始太阳辐射数据
    print("☀️ 原始太阳辐射分析（NASA POWER数据）:")
    print("-" * 40)
    
    # 将数据转换为DataFrame进行分析
    data_df = pd.DataFrame({
        'ghi': [d.ghi for d in solar_data],
        'dni': [d.dni for d in solar_data], 
        'dhi': [d.dhi for d in solar_data],
        'temp': [d.temperature for d in solar_data],
        'wind': [d.wind_speed for d in solar_data]
    })
    
    # 添加日期信息
    start_date = datetime(2024, 6, 27)  # NASA数据的起始日期
    dates = pd.date_range(start=start_date, periods=len(solar_data), freq='D')
    data_df.index = dates
    data_df['month'] = data_df.index.month
    
    # 按月分析太阳辐射
    monthly_radiation = data_df.groupby('month').agg({
        'ghi': 'mean',
        'dni': 'mean', 
        'dhi': 'mean',
        'temp': 'mean',
        'wind': 'mean'
    })
    
    print("月度平均太阳辐射（kWh/m²/day）:")
    for month in range(1, 13):
        if month in monthly_radiation.index:
            ghi = monthly_radiation.loc[month, 'ghi'] 
            temp = monthly_radiation.loc[month, 'temp']
            print(f"  {month:2d}月: GHI={ghi:5.2f}, 温度={temp:5.1f}°C")
    
    print()
    print("🔋 光伏发电量分析:")
    print("-" * 40)
    
    # 分析发电量
    monthly_gen = results['monthly_generation']
    for i, gen in enumerate(monthly_gen):
        month = i + 1
        if month in monthly_radiation.index:
            ghi = monthly_radiation.loc[month, 'ghi']
            temp = monthly_radiation.loc[month, 'temp']
            efficiency = gen / (ghi * 30) if ghi > 0 else 0  # 简化效率计算
            print(f"  {month:2d}月: {gen:4.0f} kWh, 效率因子={efficiency:5.2f}")
    
    print()
    print("🌡️ 温度对发电效率的影响分析:")
    print("-" * 40)
    
    # 分析温度系数影响
    # 硅电池板典型温度系数: -0.004/°C
    temp_coeff = -0.004
    ref_temp = 25.0  # 标准测试条件温度
    
    print("考虑温度系数的发电效率修正:")
    summer_months = [6, 7, 8]
    winter_months = [12, 1, 2]
    
    for season, months in [("夏季", summer_months), ("冬季", winter_months)]:
        total_radiation = 0
        total_gen = 0
        total_temp_factor = 0
        count = 0
        
        for month in months:
            if month in monthly_radiation.index and month <= len(monthly_gen):
                ghi = monthly_radiation.loc[month, 'ghi']
                temp = monthly_radiation.loc[month, 'temp'] 
                gen = monthly_gen[month-1]
                temp_factor = 1 + temp_coeff * (temp - ref_temp)
                
                total_radiation += ghi
                total_gen += gen
                total_temp_factor += temp_factor
                count += 1
                
                print(f"  {month}月: 辐射={ghi:.2f}, 温度={temp:.1f}°C, 温度因子={temp_factor:.3f}")
        
        if count > 0:
            avg_radiation = total_radiation / count
            avg_gen = total_gen / count
            avg_temp_factor = total_temp_factor / count
            print(f"  {season}平均: 辐射={avg_radiation:.2f}, 发电={avg_gen:.0f}, 温度因子={avg_temp_factor:.3f}")
    
    print()
    print("🔍 异常情况分析:")
    print("-" * 40)
    
    # 检查是否有物理上不合理的情况
    if 8 in monthly_radiation.index and 12 in monthly_radiation.index:
        aug_radiation = monthly_radiation.loc[8, 'ghi'] 
        dec_radiation = monthly_radiation.loc[12, 'ghi']
        aug_gen = monthly_gen[7]  # 8月 
        dec_gen = monthly_gen[11]  # 12月
        
        print(f"8月辐射: {aug_radiation:.2f} kWh/m²/day")
        print(f"12月辐射: {dec_radiation:.2f} kWh/m²/day") 
        print(f"8月发电: {aug_gen:.0f} kWh")
        print(f"12月发电: {dec_gen:.0f} kWh")
        
        radiation_ratio = aug_radiation / dec_radiation if dec_radiation > 0 else 0
        generation_ratio = aug_gen / dec_gen if dec_gen > 0 else 0
        
        print(f"辐射比值 (8月/12月): {radiation_ratio:.2f}")
        print(f"发电比值 (8月/12月): {generation_ratio:.2f}")
        
        if generation_ratio < radiation_ratio * 0.8:
            print("⚠️  发电比值明显低于辐射比值，可能原因:")
            print("   1. 温度系数影响（夏季高温降低效率）")
            print("   2. 数据处理算法问题")
            print("   3. 系统设计参数问题")
        elif generation_ratio > radiation_ratio * 1.2:
            print("⚠️  发电比值高于辐射比值，需要检查算法")

if __name__ == "__main__":
    try:
        asyncio.run(analyze_physical_patterns())
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
