"""
配置管理模块
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = "光伏参谋AI Copilot"
    debug: bool = True
    log_level: str = "INFO"
    
    # API配置
    openai_api_key: Optional[str] = None
    google_maps_api_key: Optional[str] = None
    
    # NASA POWER API配置
    nasa_power_base_url: str = "https://power.larc.nasa.gov/api/temporal/daily/point"
    
    # 光伏系统默认参数
    default_system_efficiency: float = 0.85  # 系统综合效率
    default_module_efficiency: float = 0.20  # 组件效率
    default_performance_ratio: float = 0.80  # 性能比
    default_tilt_angle: float = 30.0  # 默认倾角
    default_azimuth: float = 180.0  # 默认朝向（正南）
    
    # 计算参数
    system_lifetime_years: int = 25  # 系统寿命
    annual_degradation_rate: float = 0.005  # 年衰减率
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
