"""
数据模型定义
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class LocationInfo(BaseModel):
    """地理位置信息"""
    address: str = Field(description="原始地址输入")
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")
    altitude: Optional[float] = Field(default=None, description="海拔高度 米")
    country: Optional[str] = Field(default=None, description="国家")
    province: Optional[str] = Field(default=None, description="省份")
    city: Optional[str] = Field(default=None, description="城市")


class SolarData(BaseModel):
    """太阳辐射数据"""
    date: str = Field(description="日期")
    irradiance: float = Field(description="太阳辐射量 kWh/m²/day")
    temperature: Optional[float] = Field(default=None, description="温度 °C")
    wind_speed: Optional[float] = Field(default=None, description="风速 m/s")
    precipitation: Optional[float] = Field(default=None, description="降水量 mm")


class PVSystemParams(BaseModel):
    """光伏系统参数"""
    capacity_kw: float = Field(description="装机容量 kW")
    tilt_angle: float = Field(default=30.0, description="倾角 度")
    azimuth: float = Field(default=180.0, description="方位角 度")
    module_efficiency: float = Field(default=0.20, description="组件效率")
    system_efficiency: float = Field(default=0.85, description="系统效率")
    performance_ratio: float = Field(default=0.80, description="性能比")


class SystemParams(BaseModel):
    """系统参数 - 兼容性别名"""
    capacity_kw: float = Field(description="装机容量 kW")
    tilt_angle: float = Field(default=30.0, description="倾角 度")
    azimuth: float = Field(default=180.0, description="方位角 度")
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")


class PVResult(BaseModel):
    """PV计算结果"""
    annual_generation_kwh: float = Field(description="年发电量 kWh")
    monthly_generation: List[Dict[str, Any]] = Field(description="月度发电量数据")
    capacity_factor: float = Field(description="容量因子")
    system_efficiency: float = Field(description="系统效率")
    peak_power_kw: float = Field(description="峰值功率 kW")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据")


class PowerGeneration(BaseModel):
    """发电量数据"""
    daily_generation: List[float] = Field(description="日发电量 kWh")
    monthly_generation: List[float] = Field(description="月发电量 kWh")
    annual_generation: float = Field(description="年发电量 kWh")
    monthly_average: float = Field(description="月均发电量 kWh")


class OptimizationResult(BaseModel):
    """优化建议结果"""
    optimal_tilt: float = Field(description="最优倾角")
    optimal_azimuth: float = Field(description="最优方位角")
    generation_improvement: float = Field(description="发电量提升百分比")


class AnalysisResult(BaseModel):
    """分析结果"""
    location: LocationInfo
    system_params: PVSystemParams
    power_generation: PowerGeneration
    optimization: Optional[OptimizationResult] = None
    analysis_date: datetime = Field(default_factory=datetime.now)
    
    
class ChatMessage(BaseModel):
    """聊天消息"""
    role: str = Field(description="角色: user/assistant")
    content: str = Field(description="消息内容")
    message_type: str = Field(default="text", description="消息类型: text/card/chart")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="附加数据")
