"""
工具函数
"""

import re
import math
from typing import Tuple, Optional
from loguru import logger


def parse_coordinates(text: str) -> Optional[Tuple[float, float]]:
    """
    从文本中解析经纬度坐标
    
    Args:
        text: 输入文本
        
    Returns:
        (latitude, longitude) 或 None
    """
    # 匹配各种经纬度格式
    patterns = [
        r'(\d+\.?\d*)[°度]?\s*[NS北南]?\s*[,，]\s*(\d+\.?\d*)[°度]?\s*[EW东西]?',
        r'(\d+\.?\d*)\s*[,，]\s*(\d+\.?\d*)',
        r'lat[itude]*[:=]\s*(\d+\.?\d*)\s*[,，]?\s*lon[gitude]*[:=]\s*(\d+\.?\d*)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            try:
                lat, lon = float(match.group(1)), float(match.group(2))
                if -90 <= lat <= 90 and -180 <= lon <= 180:
                    return lat, lon
            except ValueError:
                continue
    
    return None


def calculate_optimal_tilt(latitude: float) -> float:
    """
    计算最优倾角 - 改进版
    
    Args:
        latitude: 纬度
        
    Returns:
        最优倾角（度）
    """
    import math
    
    # 基础倾角 = 纬度
    base_tilt = abs(latitude)
    
    # 根据纬度进行微调（基于实际测试数据）
    if abs(latitude) < 25:  # 热带地区
        # 热带地区倾角可以稍小，便于雨水冲洗
        optimal_tilt = base_tilt - 5
    elif abs(latitude) < 40:  # 亚热带地区  
        # 亚热带地区倾角接近纬度
        optimal_tilt = base_tilt
    elif abs(latitude) < 50:  # 温带地区
        # 温带地区可以稍大，优化冬季发电
        optimal_tilt = base_tilt + 5
    else:  # 高纬度地区
        # 高纬度地区倾角增大，但不超过60°
        optimal_tilt = min(60, base_tilt + 10)
    
    # 确保倾角在合理范围内
    optimal_tilt = max(10, min(60, optimal_tilt))
    
    return optimal_tilt


def calculate_optimal_tilt_advanced(latitude: float) -> dict:
    """
    计算最优倾角 - 高级版本（考虑季节性）
    
    Args:
        latitude: 纬度
        
    Returns:
        包含不同优化目标的倾角字典
    """
    import math
    
    abs_lat = abs(latitude)
    
    # 年度最优倾角（简化公式）
    annual_optimal = abs_lat
    
    # 冬季最优倾角（12-2月）
    winter_optimal = min(60, abs_lat + 15)
    
    # 夏季最优倾角（6-8月）  
    summer_optimal = max(10, abs_lat - 15)
    
    # 春秋最优倾角
    spring_autumn_optimal = abs_lat
    
    # 基于太阳赤纬角的精确计算
    # 冬至太阳赤纬角 ≈ -23.45°
    # 夏至太阳赤纬角 ≈ +23.45°
    
    # 冬季优化倾角
    winter_declination = -23.45
    winter_precise = abs_lat - winter_declination  # 约为纬度+23.45°
    winter_precise = max(10, min(60, winter_precise))
    
    # 夏季优化倾角
    summer_declination = 23.45
    summer_precise = abs_lat - summer_declination  # 约为纬度-23.45°
    summer_precise = max(10, min(60, summer_precise))
    
    return {
        'annual_optimal': annual_optimal,
        'winter_optimal': winter_optimal,
        'summer_optimal': summer_optimal,
        'spring_autumn_optimal': spring_autumn_optimal,
        'winter_precise': winter_precise,
        'summer_precise': summer_precise,
        'recommended': annual_optimal  # 推荐使用年度最优
    }


def calculate_azimuth_factor(azimuth: float) -> float:
    """
    计算方位角修正系数
    
    Args:
        azimuth: 方位角（度，正南为180）
        
    Returns:
        修正系数
    """
    # 正南为最优，偏离正南的损失
    deviation = abs(azimuth - 180)
    if deviation <= 45:
        return 1.0 - (deviation / 45) * 0.1  # 最大损失10%
    else:
        return 1.0 - (deviation / 90) * 0.3   # 最大损失30%


def format_number(value: float, precision: int = 1) -> str:
    """
    格式化数字显示
    
    Args:
        value: 数值
        precision: 小数位数
        
    Returns:
        格式化后的字符串
    """
    if value >= 10000:
        return f"{value/10000:.{precision}f}万"
    elif value >= 1000:
        return f"{value/1000:.{precision}f}千"
    else:
        return f"{value:.{precision}f}"


def validate_system_params(capacity_kw: float, tilt: float, azimuth: float) -> bool:
    """
    验证系统参数的合理性
    
    Args:
        capacity_kw: 装机容量
        tilt: 倾角
        azimuth: 方位角
        
    Returns:
        是否有效
    """
    return (
        0 < capacity_kw <= 10000 and  # 容量范围
        0 <= tilt <= 90 and          # 倾角范围
        0 <= azimuth <= 360          # 方位角范围
    )


def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    计算两点间距离（公里）
    
    Args:
        lat1, lon1: 第一个点的经纬度
        lat2, lon2: 第二个点的经纬度
        
    Returns:
        距离（公里）
    """
    R = 6371  # 地球半径（公里）
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)
    
    a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) +
         math.cos(lat1_rad) * math.cos(lat2_rad) *
         math.sin(delta_lon/2) * math.sin(delta_lon/2))
    
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    return R * c
