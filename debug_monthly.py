#!/usr/bin/env python3
"""
详细诊断月度数据计算的问题
检查时间索引和数据分布
"""

import asyncio
import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams


async def debug_monthly_calculation():
    """调试月度计算逻辑"""
    
    print("🔧 详细诊断月度数据计算问题")
    print("=" * 60)
    
    # 获取数据
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    location = await geo_agent.parse_location("北京市万橡悦府2期")
    solar_data = await solar_agent.get_solar_data(location)
    
    # 准备天气数据
    weather_df = pv_agent._prepare_weather_data(solar_data)
    
    print(f"📅 天气数据时间范围:")
    print(f"  开始时间: {weather_df.index[0]}")
    print(f"  结束时间: {weather_df.index[-1]}")
    print(f"  总数据点: {len(weather_df)}")
    
    # 检查每个月的数据点数量
    print(f"\n📊 各月数据点统计:")
    print("-" * 40)
    
    monthly_counts = {}
    for month in range(1, 13):
        month_data = weather_df[weather_df.index.month == month]
        monthly_counts[month] = len(month_data)
        
        if len(month_data) > 0:
            print(f"{month:>2}月: {len(month_data):>4}小时 ({month_data.index[0].strftime('%Y-%m-%d')} 到 {month_data.index[-1].strftime('%Y-%m-%d')})")
        else:
            print(f"{month:>2}月: {len(month_data):>4}小时 (无数据)")
    
    # 检查是否有跨年数据
    years = weather_df.index.year.unique()
    print(f"\n📆 涉及年份: {list(years)}")
    
    if len(years) > 1:
        print("⚠️ 警告：数据跨越多个年份，可能导致月度统计错误")
        
        for year in years:
            year_data = weather_df[weather_df.index.year == year]
            print(f"  {year}年: {len(year_data)}小时数据")
            
            # 检查该年各月数据
            for month in range(1, 13):
                month_data = year_data[year_data.index.month == month]
                if len(month_data) > 0:
                    print(f"    {month:>2}月: {len(month_data):>3}小时")
    
    # 模拟PVLib计算
    print(f"\n⚡ 模拟PVLib计算...")
    
    system_params = PVSystemParams(capacity_kw=10.0, tilt_angle=30.0, azimuth=180.0)
    
    # 获取AC功率数据
    from pvlib import pvsystem, location as pvlib_location, modelchain
    
    site_location = pvlib_location.Location(
        latitude=location.latitude,
        longitude=location.longitude,
        altitude=location.altitude or 0
    )
    
    # 创建光伏系统
    system = pvsystem.PVSystem(
        surface_tilt=system_params.tilt_angle,
        surface_azimuth=system_params.azimuth,
        module_parameters={'pdc0': system_params.capacity_kw * 1000, 'gamma_pdc': -0.004},
        inverter_parameters={'pdc0': system_params.capacity_kw * 1000}
    )
    
    # 运行模型
    mc = modelchain.ModelChain(
        system,
        site_location,
        dc_model='pvwatts',
        ac_model='pvwatts',
        aoi_model='no_loss',
        spectral_model='no_loss',
        temperature_model='sapm'
    )
    
    mc.run_model(weather_df)
    ac_power = mc.results.ac.fillna(0).clip(lower=0)
    
    print(f"AC功率数据范围: {ac_power.min():.1f} - {ac_power.max():.1f} W")
    
    # 详细检查8月和12月
    print(f"\n🔍 8月和12月详细分析:")
    print("-" * 40)
    
    aug_mask = ac_power.index.month == 8
    dec_mask = ac_power.index.month == 12
    
    aug_power = ac_power[aug_mask]
    dec_power = ac_power[dec_mask]
    
    print(f"8月数据:")
    print(f"  数据点数: {len(aug_power)}")
    print(f"  功率总和: {aug_power.sum():.0f} Wh")
    print(f"  转换为kWh: {aug_power.sum()/1000:.0f} kWh")
    if len(aug_power) > 0:
        print(f"  时间范围: {aug_power.index[0]} 到 {aug_power.index[-1]}")
        print(f"  平均功率: {aug_power.mean():.1f} W")
        print(f"  最大功率: {aug_power.max():.1f} W")
    
    print(f"\n12月数据:")
    print(f"  数据点数: {len(dec_power)}")
    print(f"  功率总和: {dec_power.sum():.0f} Wh")
    print(f"  转换为kWh: {dec_power.sum()/1000:.0f} kWh")
    if len(dec_power) > 0:
        print(f"  时间范围: {dec_power.index[0]} 到 {dec_power.index[-1]}")
        print(f"  平均功率: {dec_power.mean():.1f} W")
        print(f"  最大功率: {dec_power.max():.1f} W")
    
    # 检查原始太阳辐射数据对应
    print(f"\n☀️ 对应的太阳辐射检查:")
    print("-" * 40)
    
    aug_ghi = weather_df[aug_mask]['ghi']
    dec_ghi = weather_df[dec_mask]['ghi']
    
    print(f"8月GHI: 平均{aug_ghi.mean():.1f} W/m², 最大{aug_ghi.max():.1f} W/m²")
    print(f"12月GHI: 平均{dec_ghi.mean():.1f} W/m², 最大{dec_ghi.max():.1f} W/m²")
    
    # 建议修复方案
    print(f"\n💡 问题诊断和修复建议:")
    print("-" * 40)
    
    if len(aug_power) != len(dec_power):
        print("❌ 问题：8月和12月的数据点数量不同")
        print(f"   8月: {len(aug_power)}小时, 12月: {len(dec_power)}小时")
        print("   建议：使用标准月份小时数（8月=744小时，12月=744小时）")
    
    if len(years) > 1:
        print("❌ 问题：数据跨越多年，造成某些月份重复计算")
        print("   建议：只使用完整的12个月数据")
    
    if dec_power.sum() > aug_power.sum():
        print("❌ 异常：12月发电量高于8月")
        print("   这违反了基本的物理规律")
        print("   需要检查数据处理逻辑")


if __name__ == "__main__":
    asyncio.run(debug_monthly_calculation())
