"""
光伏参谋AI Copilot - 简化版主应用
"""

import asyncio
import chainlit as cl
from typing import Optional, Dict, Any
from loguru import logger
import sys

# 添加项目根目录到路径
sys.path.append('/Users/<USER>/Desktop/RnItem/photovoltaic')

from core.models import LocationInfo, SolarData, PowerGeneration
from core.utils import parse_coordinates, calculate_optimal_tilt
from agents.geo_agent import GeoAgent


class SimplifiedPhotovoltaicCopilot:
    """简化版光伏参谋AI Copilot"""
    
    def __init__(self):
        self.geo_agent = GeoAgent()
        self.current_location: Optional[LocationInfo] = None
        self.current_analysis: Optional[Dict[str, Any]] = None
    
    async def process_user_input(self, user_input: str) -> str:
        """处理用户输入"""
        user_input = user_input.strip()
        
        # 特殊命令处理
        if user_input.lower() in ['帮助', 'help', '?']:
            return self._get_help_message()
        elif user_input.lower() in ['示例', 'demo', 'example']:
            user_input = "北京天安门"
        
        # 地址解析和分析
        return await self._analyze_location(user_input)
    
    async def _analyze_location(self, location_input: str) -> str:
        """分析位置的光伏潜力"""
        
        # 1. 地理解析
        await cl.Message(content="🔍 正在解析地理位置...").send()
        
        location = await self.geo_agent.parse_location(location_input)
        if not location:
            return "❌ 抱歉，无法识别您输入的地址。请尝试输入更具体的地址或经纬度坐标。"
        
        self.current_location = location
        
        await cl.Message(
            content=f"📍 已解析：{location.address} ({location.latitude:.4f}, {location.longitude:.4f})"
        ).send()
        
        # 2. 获取太阳辐射数据
        await cl.Message(content="☀️ 正在获取太阳辐射数据...").send()
        
        # 尝试获取真实气象数据
        try:
            from agents.simple_solar_agent import SolarAgent
            solar_agent = SolarAgent()
            real_solar_data = await solar_agent.get_solar_data(location)
            
            if real_solar_data and len(real_solar_data) >= 6:
                solar_data = self._convert_real_solar_data(real_solar_data)
                if solar_data.get('data_source') == 'NASA POWER API':
                    await cl.Message(content="✅ 已获取NASA POWER真实气象数据").send()
                else:
                    await cl.Message(content="📊 使用智能估算气象数据").send()
            else:
                raise Exception("数据不足")
                
        except Exception as e:
            logger.warning(f"获取气象数据异常: {e}")
            await cl.Message(content="📊 使用基于纬度的估算数据").send()
            solar_data = self._estimate_solar_data(location)
        
        # 3. 计算光伏发电量
        await cl.Message(content="⚡ 正在计算光伏发电潜力...").send()
        
        # 检查用户输入是否包含容量信息
        capacity = self._extract_capacity_from_input(location_input)
        generation_data = self._calculate_simplified_generation(location, solar_data, capacity)
        
        # 4. 生成结果
        await cl.Message(content="📊 正在生成分析报告...").send()
        
        # 保存分析结果
        self.current_analysis = {
            'location': location,
            'solar_data': solar_data,
            'generation': generation_data
        }
        
        # 发送结果卡片
        await self._send_result_card(generation_data)
        
        return ""
    
    def _estimate_solar_data(self, location: LocationInfo) -> Dict[str, Any]:
        """估算太阳辐射数据"""
        import math
        
        latitude = location.latitude
        abs_lat = abs(latitude)
        
        # 基于纬度估算年度GHI
        if abs_lat < 20:
            base_annual_ghi = 5.5  # 热带地区
            base_temp = 28
        elif abs_lat < 40:
            base_annual_ghi = 4.5  # 亚热带地区
            base_temp = 18
        elif abs_lat < 60:
            base_annual_ghi = 3.5  # 温带地区
            base_temp = 8
        else:
            base_annual_ghi = 2.5  # 寒带地区
            base_temp = -2
        
        # 生成月度数据
        monthly_ghi = {}
        monthly_temp = {}
        
        for month in range(1, 13):
            # 季节性调节
            day_of_year = month * 30 - 15
            declination = 23.45 * math.sin(math.radians(360 * (284 + day_of_year) / 365))
            
            # 太阳高度因子
            lat_rad = math.radians(latitude)
            dec_rad = math.radians(declination)
            elevation_factor = math.sin(lat_rad) * math.sin(dec_rad) + math.cos(lat_rad) * math.cos(dec_rad)
            
            # 月度GHI
            seasonal_factor = max(0.4, (elevation_factor + 1) / 2)
            monthly_ghi[month] = base_annual_ghi * seasonal_factor
            
            # 月度温度
            if latitude >= 0:  # 北半球
                temp_variation = 12 * math.cos(math.radians((month - 7) * 30))
            else:  # 南半球
                temp_variation = 12 * math.cos(math.radians((month - 1) * 30))
            
            monthly_temp[month] = base_temp + temp_variation
        
        return {
            'monthly_ghi': monthly_ghi,
            'monthly_temperature': monthly_temp,
            'annual_ghi': sum(monthly_ghi.values()) / 12
        }
    
    def _extract_capacity_from_input(self, user_input: str) -> Optional[float]:
        """从用户输入中提取系统容量"""
        import re
        
        # 匹配类似 "5kW", "3.5KW", "10千瓦" 的模式
        patterns = [
            r'(\d+\.?\d*)\s*kw',
            r'(\d+\.?\d*)\s*KW', 
            r'(\d+\.?\d*)\s*千瓦',
            r'(\d+\.?\d*)\s*kva',
            r'(\d+\.?\d*)\s*KVA'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                try:
                    capacity = float(match.group(1))
                    # 限制容量范围在合理区间
                    if 0.5 <= capacity <= 100:  # 0.5kW到100kW
                        return capacity
                except ValueError:
                    continue
        
        return None  # 没有找到容量信息，使用默认值
    
    def _convert_real_solar_data(self, real_solar_data) -> Dict[str, Any]:
        """转换真实气象数据为项目使用的格式"""
        try:
            # 如果是SolarData列表（来自solar_agent）
            if isinstance(real_solar_data, list) and real_solar_data:
                # 按月计算平均值
                monthly_ghi = {}
                monthly_temp = {}
                
                # 将数据按月分组
                monthly_data = {}
                for data in real_solar_data:
                    month = int(data.date[4:6])  # YYYYMMDD格式
                    if month not in monthly_data:
                        monthly_data[month] = []
                    monthly_data[month].append(data)
                
                # 计算月度平均值
                for month in range(1, 13):
                    if month in monthly_data:
                        month_data = monthly_data[month]
                        monthly_ghi[month] = sum(d.irradiance for d in month_data) / len(month_data)
                        monthly_temp[month] = sum(d.temperature for d in month_data if d.temperature) / len([d for d in month_data if d.temperature])
                    else:
                        # 使用相邻月份数据估算
                        monthly_ghi[month] = 4.0  # 默认值
                        monthly_temp[month] = 20.0  # 默认值
                
                return {
                    'monthly_ghi': monthly_ghi,
                    'monthly_temperature': monthly_temp,
                    'annual_ghi': sum(monthly_ghi.values()) / 12,
                    'data_source': 'NASA POWER API'
                }
            
            # 如果是SolarData对象（来自新版本的agent）
            elif hasattr(real_solar_data, 'ghi_monthly'):
                return {
                    'monthly_ghi': real_solar_data.ghi_monthly,
                    'monthly_temperature': real_solar_data.temperature_monthly,
                    'annual_ghi': real_solar_data.annual_ghi,
                    'data_source': 'NASA POWER API'
                }
            
            else:
                raise ValueError("不支持的数据格式")
                
        except Exception as e:
            logger.error(f"转换真实气象数据失败: {e}")
            # 返回估算数据作为备用
            return self._estimate_solar_data(LocationInfo(address="", latitude=0, longitude=0))
    
    def _calculate_simplified_generation(self, location: LocationInfo, solar_data: Dict[str, Any], 
                                       custom_capacity: float = None) -> Dict[str, Any]:
        """简化的发电量计算"""
        
        # 系统参数 - 支持自定义容量
        system_capacity = custom_capacity if custom_capacity else 5.0  # 默认5kW
        system_efficiency = 0.85
        optimal_tilt = calculate_optimal_tilt(location.latitude)
        
        # 计算月度发电量
        monthly_generation = {}
        for month in range(1, 13):
            ghi = solar_data['monthly_ghi'][month]
            temperature = solar_data['monthly_temperature'][month]
            
            # 倾斜面修正
            tilt_factor = 1.1 if optimal_tilt > 20 else 1.0
            
            # 温度修正
            temp_factor = 1 + (-0.004) * (temperature - 25)
            
            # 月度天数
            days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month - 1]
            
            # 月度发电量
            monthly_generation[month] = (
                ghi * tilt_factor * temp_factor * 
                system_capacity * system_efficiency * days_in_month
            )
        
        # 年度统计
        annual_generation = sum(monthly_generation.values())
        daily_average = annual_generation / 365
        capacity_factor = annual_generation / (system_capacity * 8760)
        specific_yield = annual_generation / system_capacity
        
        # 最佳/最差月份
        best_month_num = max(monthly_generation, key=monthly_generation.get)
        worst_month_num = min(monthly_generation, key=monthly_generation.get)
        
        month_names = ['', '1月', '2月', '3月', '4月', '5月', '6月',
                      '7月', '8月', '9月', '10月', '11月', '12月']
        
        # 效率评级
        if specific_yield > 1400:
            grade = "优秀"
        elif specific_yield > 1200:
            grade = "良好"
        elif specific_yield > 1000:
            grade = "一般"
        elif specific_yield > 800:
            grade = "较差"
        else:
            grade = "很差"
        
        # 经济计算
        electricity_price = 0.65  # 元/kWh
        annual_revenue = annual_generation * electricity_price
        system_cost = system_capacity * 5000  # 5元/W
        payback_period = system_cost / annual_revenue if annual_revenue > 0 else 0
        
        return {
            'system_capacity': system_capacity,
            'optimal_tilt': optimal_tilt,
            'annual_generation': annual_generation,
            'monthly_generation': monthly_generation,
            'daily_average': daily_average,
            'capacity_factor': capacity_factor,
            'specific_yield': specific_yield,
            'best_month': month_names[best_month_num],
            'best_month_generation': monthly_generation[best_month_num],
            'worst_month': month_names[worst_month_num],
            'worst_month_generation': monthly_generation[worst_month_num],
            'efficiency_grade': grade,
            'annual_revenue': annual_revenue,
            'payback_period': payback_period,
            'total_revenue_25years': annual_revenue * 25 * 0.9  # 考虑衰减
        }
    
    async def _send_result_card(self, generation_data: Dict[str, Any]):
        """发送结果卡片"""
        
        # 主要结果卡片
        card_content = f"""## 📊 **光伏发电潜力分析结果**

### 🎯 **核心指标**
- **年发电量：** {generation_data['annual_generation']:,.0f} kWh
- **日均发电：** {generation_data['daily_average']:.1f} kWh  
- **系统容量：** {generation_data['system_capacity']} kW
- **效率评级：** {generation_data['efficiency_grade']}

### 📈 **发电表现**
- **最佳月份：** {generation_data['best_month']} ({generation_data['best_month_generation']:,.0f} kWh)
- **最低月份：** {generation_data['worst_month']} ({generation_data['worst_month_generation']:,.0f} kWh)
- **容量因子：** {generation_data['capacity_factor']:.1%}
- **比发电量：** {generation_data['specific_yield']:.0f} kWh/kWp

### 💰 **经济效益**
- **年发电收入：** ¥{generation_data['annual_revenue']:,.0f}
- **投资回收期：** {generation_data['payback_period']:.1f} 年
- **25年总收益：** ¥{generation_data['total_revenue_25years']:,.0f}

### 🔧 **安装建议**
- **最佳倾角：** {generation_data['optimal_tilt']:.1f}°
- **最佳朝向：** 南向 (180°)
- **安装建议：** 固定式安装，定期清洁维护
        """
        
        await cl.Message(content=card_content).send()
        
        # 添加操作按钮
        actions = [
            cl.Action(name="show_monthly", value="monthly", label="📈 月度详情", payload={"action": "monthly"}),
            cl.Action(name="show_optimization", value="optimization", label="🔧 优化建议", payload={"action": "optimization"}),
            cl.Action(name="export_report", value="export", label="📄 导出报告", payload={"action": "export"})
        ]
        
        await cl.Message(
            content="💡 **点击下方按钮获取更多信息：**",
            actions=actions
        ).send()
    
    def _get_help_message(self) -> str:
        """获取帮助信息"""
        return """
🌟 **光伏参谋AI Copilot使用指南**

**输入方式：**
• **地址：** 如"北京天安门"、"上海浦东新区"
• **坐标：** 如"39.9042, 116.4074"
• **带容量：** 如"北京天安门 10kW"、"上海浦东新区 3.5千瓦"

**系统容量设置：**
• **默认容量：** 5kW（家用标准）
• **支持范围：** 0.5kW - 100kW
• **容量格式：** 5kW、3.5KW、10千瓦、8kva

**功能命令：**
• **"示例"** - 运行北京天安门示例分析
• **"帮助"** - 显示此帮助信息

**使用示例：**
• 北京朝阳区
• 上海市浦东新区陆家嘴 8kW
• 广州市天河区 15千瓦
• 39.9042, 116.4074 20kW

**分析内容：**
✅ 年发电量预测
✅ 月度发电分布  
✅ 经济效益分析
✅ 系统优化建议
✅ 安装参数推荐

请输入您想分析的地点（可包含系统容量）开始使用！
        """


# 全局应用实例
app = SimplifiedPhotovoltaicCopilot()


@cl.on_chat_start
async def start():
    """聊天开始时的欢迎消息"""
    welcome_message = """
🌞 **欢迎使用光伏参谋AI Copilot！**

我是您的专业光伏发电量评估助手，可以帮您：

✨ **快速评估** 任意地点的光伏发电潜力
📊 **精确计算** 年度和月度发电量预测  
🎯 **智能优化** 系统安装角度和朝向
💰 **经济分析** 投资回收期和收益预测

**🚀 快速开始：**

请输入您想评估的地点（可指定系统容量）：

**示例输入：**
• 北京天安门 **（默认5kW）**
• 上海浦东新区 **10kW**
• 广州天河区 **3.5千瓦**
• 39.9042, 116.4074 **20kW**
• 或输入"示例"查看演示

💡 **默认系统容量：5kW**（家用标准）
支持范围：0.5kW - 100kW

让我们开始您的光伏评估之旅吧！ 🎯
    """
    
    await cl.Message(content=welcome_message).send()


@cl.on_message
async def main(message: cl.Message):
    """处理用户消息"""
    try:
        response = await app.process_user_input(message.content)
        if response:
            await cl.Message(content=response).send()
    except Exception as e:
        logger.error(f"处理消息时出错: {e}")
        await cl.Message(content="❌ 处理请求时出现错误，请稍后重试。").send()


@cl.action_callback("show_monthly")
async def on_show_monthly(action):
    """显示月度详情"""
    if not app.current_analysis:
        await cl.Message(content="❌ 请先进行分析").send()
        return
    
    generation = app.current_analysis['generation']
    monthly_data = generation['monthly_generation']
    
    # 生成月度数据表格
    months = ['1月', '2月', '3月', '4月', '5月', '6月',
             '7月', '8月', '9月', '10月', '11月', '12月']
    
    table_content = """## 📈 **月度发电量详情**

| 月份 | 发电量(kWh) | 占年度比例 |
|------|------------|-----------|"""
    
    for i, month in enumerate(months):
        month_gen = monthly_data.get(i+1, 0)
        percentage = month_gen / generation['annual_generation'] * 100
        table_content += f"\n| {month} | {month_gen:,.0f} | {percentage:.1f}% |"
    
    table_content += f"""

**📊 发电分布分析：**
- **峰值月份：** {generation['best_month']} ({generation['best_month_generation']:,.0f} kWh)
- **低谷月份：** {generation['worst_month']} ({generation['worst_month_generation']:,.0f} kWh)
- **季节波动：** {(generation['best_month_generation']/generation['worst_month_generation']-1)*100:.0f}%

**💡 建议：** 合理规划用电负荷，在发电高峰期最大化自用比例。
    """
    
    await cl.Message(content=table_content).send()


@cl.action_callback("show_optimization")
async def on_show_optimization(action):
    """显示优化建议"""
    if not app.current_analysis:
        await cl.Message(content="❌ 请先进行分析").send()
        return
    
    location = app.current_analysis['location']
    generation = app.current_analysis['generation']
    
    optimization_content = f"""## 🔧 **系统优化建议**

### 📐 **安装参数优化**
- **推荐倾角：** {generation['optimal_tilt']:.1f}°
- **推荐朝向：** 正南方向 (方位角180°)
- **安装方式：** 固定式安装

### 🎯 **倾角选择原则**
根据您的位置（纬度 {location.latitude:.2f}°），推荐倾角为 {generation['optimal_tilt']:.1f}°

**计算依据：**
- **基础公式：** 最优倾角 ≈ 纬度绝对值
- **纬度修正：** 根据气候带进行微调
- **实用考虑：** 兼顾发电效率与安装维护

**季节性调节建议：**
- **冬季发电优化：** 可增加倾角至 {generation['optimal_tilt']+15:.1f}°
- **夏季发电优化：** 可减少倾角至 {max(10, generation['optimal_tilt']-15):.1f}°
- **全年最优：** 当前推荐角度 {generation['optimal_tilt']:.1f}°

### 🔄 **进阶优化选项**
- **单轴跟踪：** 可提升发电量15-25%，但需考虑成本
- **季节调节：** 春秋两季手动调整倾角，可提升3-5%
- **清洁维护：** 定期清洁可避免2-5%的发电损失

### 🏗️ **安装建议**
- **屋顶安装：** 确保屋顶承重能力足够
- **地面安装：** 注意避开阴影遮挡
- **间距设计：** 前后排间距至少4米，避免冬季阴影
- **通风散热：** 组件底部留足散热空间

### ⚡ **系统配置建议**
- **逆变器选型：** 选择效率>98%的组串式逆变器
- **电缆规格：** 使用符合标准的光伏专用电缆
- **监控系统：** 建议安装发电量监控系统
    """
    
    await cl.Message(content=optimization_content).send()


@cl.action_callback("export_report")  
async def on_export_report(action):
    """导出报告"""
    if not app.current_analysis:
        await cl.Message(content="❌ 请先进行分析").send()
        return
    
    # 生成简化报告
    location = app.current_analysis['location']
    generation = app.current_analysis['generation']
    
    report_content = f"""# 光伏发电评估报告

**项目地址：** {location.address}
**报告生成时间：** {cl.Message.created_at if hasattr(cl.Message, 'created_at') else '2024年'}

## 核心指标
- 年发电量：{generation['annual_generation']:,.0f} kWh
- 系统容量：{generation['system_capacity']} kW
- 年发电收入：¥{generation['annual_revenue']:,.0f}
- 投资回收期：{generation['payback_period']:.1f} 年

## 月度发电量
"""
    
    months = ['1月', '2月', '3月', '4月', '5月', '6月',
             '7月', '8月', '9月', '10月', '11月', '12月']
    
    for i, month in enumerate(months):
        month_gen = generation['monthly_generation'].get(i+1, 0)
        report_content += f"- {month}：{month_gen:,.0f} kWh\n"
    
    report_content += f"""
## 技术建议
- 最佳倾角：{generation['optimal_tilt']:.1f}°
- 最佳朝向：正南方向
- 效率评级：{generation['efficiency_grade']}

---
报告由光伏参谋AI Copilot生成
    """
    
    await cl.Message(content="📄 **报告内容如下：**").send()
    await cl.Message(content=f"```\n{report_content}\n```").send()
    await cl.Message(content="💡 您可以复制上述内容保存为文本文件。").send()


if __name__ == "__main__":
    logger.info("启动光伏参谋AI Copilot")
