#!/usr/bin/env python3
"""
发电量数据分析测试脚本
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams
import pandas as pd
import numpy as np


async def analyze_power_generation():
    """分析发电量数据的准确性"""
    print("🔍 开始分析发电量数据...")
    
    # 初始化agents
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    # 测试北京市数据
    location_info = await geo_agent.parse_location("北京市 5kW")
    print(f"📍 位置信息: {location_info.city}, 纬度={location_info.latitude:.2f}°, 经度={location_info.longitude:.2f}°, 海拔={location_info.altitude}m")
    
    # 获取太阳能数据
    solar_data = await solar_agent.get_solar_data(location_info)
    print(f"☀️ 获取到{len(solar_data)}天的太阳能数据")
    
    # 分析太阳辐射数据
    irradiances = [data.irradiance for data in solar_data if data.irradiance is not None]
    temperatures = [data.temperature for data in solar_data if data.temperature is not None]
    
    print(f"\n📊 太阳辐射统计:")
    print(f"   平均辐射: {np.mean(irradiances):.2f} kWh/m²/day")
    print(f"   最大辐射: {np.max(irradiances):.2f} kWh/m²/day")
    print(f"   最小辐射: {np.min(irradiances):.2f} kWh/m²/day")
    print(f"   标准差: {np.std(irradiances):.2f} kWh/m²/day")
    
    print(f"\n🌡️ 温度统计:")
    print(f"   平均温度: {np.mean(temperatures):.1f}°C")
    print(f"   最高温度: {np.max(temperatures):.1f}°C")
    print(f"   最低温度: {np.min(temperatures):.1f}°C")
    
    # 按月份分析辐射数据
    monthly_irradiance = {}
    for data in solar_data:
        if data.irradiance is not None:
            month = int(data.date[4:6])  # 提取月份
            if month not in monthly_irradiance:
                monthly_irradiance[month] = []
            monthly_irradiance[month].append(data.irradiance)
    
    print(f"\n📅 按月份辐射统计:")
    for month in sorted(monthly_irradiance.keys()):
        avg_irr = np.mean(monthly_irradiance[month])
        print(f"   {month:2d}月: {avg_irr:.2f} kWh/m²/day ({len(monthly_irradiance[month])}天)")
    
    # 系统参数
    system_params = PVSystemParams(
        capacity_kw=5.0,
        module_efficiency=0.20,
        system_efficiency=0.85,
        performance_ratio=0.80,
        tilt_angle=abs(location_info.latitude),
        azimuth=180
    )
    
    print(f"\n⚡ 系统参数:")
    print(f"   装机容量: {system_params.capacity_kw}kW")
    print(f"   倾斜角度: {system_params.tilt_angle:.1f}°")
    print(f"   方位角: {system_params.azimuth}°")
    
    # 计算发电量
    try:
        power_result = await pv_agent.calculate_power_generation(
            location_info, solar_data, system_params
        )
        
        if power_result:
            print(f"\n📈 发电量结果:")
            print(f"   年发电量: {power_result.annual_generation:.0f} kWh")
            print(f"   月平均: {power_result.monthly_average:.0f} kWh")
            print(f"   日平均: {power_result.annual_generation/365:.1f} kWh/day")
            
            # 分析月度发电量变化
            if hasattr(power_result, 'monthly_generation') and power_result.monthly_generation:
                print(f"\n📊 月度发电量分析:")
                monthly_gen = power_result.monthly_generation
                for i, gen in enumerate(monthly_gen):
                    print(f"   {i+1:2d}月: {gen:.0f} kWh")
                
                # 计算变异系数
                cv = np.std(monthly_gen) / np.mean(monthly_gen) * 100
                print(f"\n📊 发电量变异性:")
                print(f"   标准差: {np.std(monthly_gen):.0f} kWh")
                print(f"   变异系数: {cv:.1f}%")
                print(f"   最大/最小比值: {np.max(monthly_gen)/np.min(monthly_gen):.1f}")
                
                # 分析是否合理
                if cv > 50:
                    print("⚠️  变异系数过高，可能存在问题")
                elif cv < 20:
                    print("✅ 发电量变化合理")
                else:
                    print("⚖️  发电量变化在正常范围内")
        
    except Exception as e:
        print(f"❌ 发电量计算失败: {e}")
        
        # 尝试简化计算验证
        print(f"\n🔧 尝试简化计算验证...")
        daily_avg_irr = np.mean(irradiances)
        estimated_daily_gen = daily_avg_irr * system_params.capacity_kw * system_params.system_efficiency
        estimated_annual_gen = estimated_daily_gen * 365
        print(f"   估算日发电量: {estimated_daily_gen:.1f} kWh/day")
        print(f"   估算年发电量: {estimated_annual_gen:.0f} kWh")

if __name__ == "__main__":
    asyncio.run(analyze_power_generation())
