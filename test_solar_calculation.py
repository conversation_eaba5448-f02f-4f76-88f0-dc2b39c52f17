#!/usr/bin/env python3
"""
测试新的太阳辐射计算功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.solar_agent import SolarAgent
from core.models import LocationInfo

async def test_solar_calculation():
    """测试太阳辐射计算"""
    print("=== 测试科学太阳辐射计算 ===")
    
    # 测试几个不同地点
    test_locations = [
        ("北京", LocationInfo(city="北京", address="北京市", latitude=39.9042, longitude=116.4074)),
        ("三河", LocationInfo(city="三河", address="河北省三河市", latitude=39.98, longitude=117.07)),
        ("上海", LocationInfo(city="上海", address="上海市", latitude=31.2304, longitude=121.4737)),
        ("深圳", LocationInfo(city="深圳", address="广东省深圳市", latitude=22.5431, longitude=114.0579)),
    ]
    
    agent = SolarAgent()
    
    for city_name, location in test_locations:
        print(f"\n--- 测试 {city_name} ---")
        try:
            solar_data = await agent.get_solar_data(location, years=1)
            
            if solar_data and len(solar_data) > 0:
                # 计算统计信息
                irradiances = [data.irradiance for data in solar_data]
                temperatures = [data.temperature for data in solar_data]
                
                avg_irradiance = sum(irradiances) / len(irradiances)
                max_irradiance = max(irradiances)
                min_irradiance = min(irradiances)
                
                avg_temperature = sum(temperatures) / len(temperatures)
                max_temperature = max(temperatures)
                min_temperature = min(temperatures)
                
                print(f"✅ 成功获取 {len(solar_data)} 天数据")
                print(f"   平均日辐射: {avg_irradiance:.2f} kWh/m²/天")
                print(f"   辐射范围: {min_irradiance:.2f} - {max_irradiance:.2f} kWh/m²/天")
                print(f"   平均温度: {avg_temperature:.1f}°C")
                print(f"   温度范围: {min_temperature:.1f} - {max_temperature:.1f}°C")
                
                # 显示春夏秋冬的典型数据
                seasons = {
                    "春季": solar_data[80],  # 3月下旬
                    "夏季": solar_data[170], # 6月下旬  
                    "秋季": solar_data[260], # 9月下旬
                    "冬季": solar_data[350]  # 12月下旬
                }
                
                print(f"   季节数据:")
                for season, data in seasons.items():
                    print(f"     {season}: {data.irradiance:.2f} kWh/m²/天, {data.temperature:.1f}°C")
                    
            else:
                print(f"❌ 未获取到数据")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_solar_calculation())
