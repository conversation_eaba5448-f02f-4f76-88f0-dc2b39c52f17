#!/usr/bin/env python3
"""
直接测试光伏发电量计算
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams, SystemParams


async def test_full_calculation():
    """测试完整的计算流程"""
    print("🧪 开始测试光伏发电量计算...")
    
    try:
        # 1. 地理解析
        geo_agent = GeoAgent()
        location = await geo_agent.parse_location("北京市万橡悦府2期")
        print(f"📍 位置解析: {location.latitude:.4f}, {location.longitude:.4f}")
        
        # 2. 获取NASA数据
        solar_agent = SolarAgent()
        solar_data = await solar_agent.get_solar_data(location)
        print(f"🛰️ NASA数据: {len(solar_data)}天")
        
        # 3. 分析原始NASA数据的季节性
        print("\n📊 原始NASA数据月度分析:")
        monthly_nasa = {}
        for data in solar_data:
            month = int(data.date[4:6])
            if month not in monthly_nasa:
                monthly_nasa[month] = []
            monthly_nasa[month].append(data.irradiance)
        
        for month in sorted(monthly_nasa.keys()):
            if monthly_nasa[month]:
                avg = sum(monthly_nasa[month]) / len(monthly_nasa[month])
                print(f"  {month}月: {avg:.2f} kWh/m²/day ({len(monthly_nasa[month])}天)")
        
        # 4. PVLib计算
        pv_agent = PVAgent()
        system_params = PVSystemParams(capacity_kw=10.0)
        
        print(f"\n⚡ 开始PVLib计算...")
        # 创建SystemParams对象
        system_params_new = SystemParams(
            capacity_kw=10.0,
            tilt_angle=30.0,
            azimuth=180.0,
            latitude=location.latitude,
            longitude=location.longitude
        )

        generation = await pv_agent.calculate_pv_generation(system_params_new, solar_data)
        
        if generation:
            print(f"\n📈 计算结果:")
            print(f"  年发电量: {generation.annual_generation_kwh:.0f} kWh")
            print(f"  容量因子: {generation.capacity_factor:.1%}")
            print(f"  系统效率: {generation.system_efficiency:.1%}")
            print(f"  峰值功率: {generation.peak_power_kw:.1f} kW")
            
            print(f"\n📅 月度发电量:")
            months = ['1月', '2月', '3月', '4月', '5月', '6月',
                     '7月', '8月', '9月', '10月', '11月', '12月']
            
            for monthly in generation.monthly_generation:
                month_name = months[monthly['month'] - 1]
                print(f"  {month_name}: {monthly['generation_kwh']:,.0f} kWh")
            
            # 5. 验证季节性
            print(f"\n🔍 季节性验证:")
            monthly_dict = {m['month']: m['generation_kwh'] for m in generation.monthly_generation}
            summer_avg = (monthly_dict.get(6, 0) + monthly_dict.get(7, 0) + monthly_dict.get(8, 0)) / 3  # 6-8月
            winter_avg = (monthly_dict.get(12, 0) + monthly_dict.get(1, 0) + monthly_dict.get(2, 0)) / 3  # 12-2月

            print(f"  夏季平均(6-8月): {summer_avg:.0f} kWh")
            print(f"  冬季平均(12-2月): {winter_avg:.0f} kWh")

            # 添加系统信息
            print(f"\n🔧 系统信息:")
            if generation.metadata:
                print(f"  组件: {generation.metadata.get('module_name', 'N/A')}")
                print(f"  逆变器: {generation.metadata.get('inverter_name', 'N/A')}")
                print(f"  实际DC容量: {generation.metadata.get('actual_dc_capacity_kw', 0):.1f} kW")
                print(f"  DC/AC比: {generation.metadata.get('dc_ac_ratio', 0):.2f}")
                print(f"  年GHI总量: {generation.metadata.get('annual_ghi_kwh_m2', 0):.0f} kWh/m²")
            
            if summer_avg > winter_avg:
                print(f"  ✅ 季节性正确: 夏季 > 冬季")
            else:
                print(f"  ❌ 季节性错误: 夏季 <= 冬季")
                
        else:
            print("❌ PVLib计算失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_full_calculation())
