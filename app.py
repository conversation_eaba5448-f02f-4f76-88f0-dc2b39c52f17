"""
光伏参谋AI Copilot主应用
"""

import asyncio
import json
from typing import Optional

import chainlit as cl
from loguru import logger

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from agents.report_agent import ReportAgent
from agents.ai_advisor import AIAdvisor
from core.models import PVSystemParams, AnalysisResult
from core.config import settings


class PhotovoltaicCopilot:
    """光伏参谋AI Copilot主类"""
    
    def __init__(self):
        self.geo_agent = GeoAgent()
        self.solar_agent = SolarAgent()
        self.pv_agent = PVAgent()
        self.report_agent = ReportAgent()
        self.ai_advisor = AIAdvisor()
        
        # 用户会话状态
        self.current_analysis: Optional[AnalysisResult] = None
    
    async def process_user_input(self, user_input: str) -> str:
        """
        处理用户输入
        
        Args:
            user_input: 用户输入的文本
            
        Returns:
            回复消息
        """
        user_input = user_input.strip()
        
        # 检查是否是特殊命令
        if user_input.lower() in ['展开详细', '详细信息', 'details']:
            return await self.show_detailed_analysis()
        elif user_input.lower() in ['导出报告', '生成报告', 'export']:
            return await self.export_report()
        elif user_input.lower() in ['帮助', 'help', '?']:
            return self.get_help_message()
        # AI功能已移除
        
        # 检查是否是针对现有分析的问题
        if self.current_analysis and any(word in user_input for word in ['多少', '怎么', '为什么', '如何', '?', '？']):
            return await self.answer_user_question(user_input)
        
        # 尝试解析为地址或坐标
        return await self.analyze_location(user_input)
    
    async def analyze_location(self, location_input: str) -> str:
        """分析指定位置的光伏潜力"""
        
        # 发送处理中消息
        await cl.Message(content="📍 正在解析位置...").send()
        
        # 1. 地理解析
        location = await self.geo_agent.parse_location(location_input)
        if not location:
            return "❌ 无法通过真实地理编码服务解析您的地址。请输入更准确的地址信息，或检查网络连接。"
        
        await cl.Message(
            content=f"📍 已解析：{location.latitude:.4f}, {location.longitude:.4f} ⏳ 获取真实气象数据中..."
        ).send()
        
        # 2. 获取真实太阳辐射数据（仅NASA数据）
        solar_data = await self.solar_agent.get_solar_data(location)
        if not solar_data:
            return "❌ 无法获取该地区的真实NASA气象数据。可能原因：网络连接问题、该地区数据不可用、或NASA服务暂时不可用。请稍后重试或更换地点。"
        
        # 3. 提取系统容量（如果用户指定了）
        capacity_kw = self._extract_capacity(location_input)
        
        # 4. 设置系统参数
        system_params = PVSystemParams(
            capacity_kw=capacity_kw,
            tilt_angle=settings.default_tilt_angle,
            azimuth=settings.default_azimuth,
            module_efficiency=settings.default_module_efficiency,
            system_efficiency=settings.default_system_efficiency,
            performance_ratio=settings.default_performance_ratio
        )
        
        # 5. 计算发电量（使用重写的PVLib专业模型）
        # 创建新的SystemParams对象
        from core.models import SystemParams
        system_params_new = SystemParams(
            capacity_kw=system_params.capacity_kw,
            tilt_angle=system_params.tilt_angle,
            azimuth=system_params.azimuth,
            latitude=location.latitude,
            longitude=location.longitude
        )

        power_generation = await self.pv_agent.calculate_pv_generation(
            system_params_new, solar_data
        )
        if not power_generation:
            return "❌ 发电量计算失败。可能原因：PVLib专业模型不可用或计算参数错误。请检查系统环境。"

        # 6. 转换PVResult为PowerGeneration格式
        from core.models import PowerGeneration
        monthly_data = [m['generation_kwh'] for m in power_generation.monthly_generation]
        # 确保有12个月的数据
        while len(monthly_data) < 12:
            monthly_data.append(0.0)

        power_gen_legacy = PowerGeneration(
            daily_generation=[],  # 暂时为空
            monthly_generation=monthly_data,
            annual_generation=power_generation.annual_generation_kwh,
            monthly_average=power_generation.annual_generation_kwh / 12
        )

        # 7. 跳过优化分析（暂时）
        optimization = None

        # 8. 生成分析结果
        self.current_analysis = AnalysisResult(
            location=location,
            system_params=system_params,
            power_generation=power_gen_legacy,
            optimization=optimization
        )

        # 保存原始PVResult用于详细显示
        self.current_pv_result = power_generation
        
        # 8. 生成结果卡片
        return await self.generate_result_card()
    
    def _extract_capacity(self, user_input: str) -> float:
        """从用户输入中提取系统容量"""
        import re
        
        # 查找类似 "5kw", "10kW", "3.5 kw" 的模式
        capacity_pattern = r'(\d+(?:\.\d+)?)\s*kw?'
        matches = re.findall(capacity_pattern, user_input, re.IGNORECASE)
        
        if matches:
            try:
                capacity = float(matches[0])
                logger.info(f"从用户输入中提取到容量: {capacity}kW")
                return capacity
            except ValueError:
                pass
        
        # 默认返回10kW
        return 10.0
    
    async def generate_result_card(self) -> str:
        """生成结果卡片"""
        if not self.current_analysis:
            return "❌ 没有可用的分析结果"
        
        result = self.current_analysis
        generation = result.power_generation
        optimization = result.optimization
        
        # 构建结果消息
        # 构建卡片内容
        optimization_text = ""
        if optimization:
            optimization_text = f"🎯 **建议倾角：** {optimization.optimal_tilt:.1f}°（+{optimization.generation_improvement:.1f}%增益）\n\n"

        # 显示详细的系统信息
        system_info = ""
        if hasattr(self, 'current_pv_result') and self.current_pv_result and self.current_pv_result.metadata:
            meta = self.current_pv_result.metadata
            system_info = f"""
🔧 **系统配置：**
- 组件：{meta.get('module_name', 'N/A').split('__')[0]} ({meta.get('actual_dc_capacity_kw', 0):.1f}kW)
- 容量因子：{self.current_pv_result.capacity_factor:.1%}
- 系统效率：{self.current_pv_result.system_efficiency:.1%}
"""

        card_content = f"""
📈 **年发电量：** {generation.annual_generation:,.0f} kWh

🔋 **月均发电：** {generation.monthly_average:,.0f} kWh

{optimization_text}💡 **系统容量：** {result.system_params.capacity_kw}kW 光伏系统
{system_info}
        """
        
        # 发送结果卡片
        await cl.Message(content=card_content).send()
        
        # 添加操作按钮
        actions = [
            cl.Action(name="show_details", value="details", label="📊 展开详细", payload={}),
            cl.Action(name="export_pdf", value="pdf", label="📄 导出PDF", payload={}),
            cl.Action(name="export_html", value="html", label="📄 导出HTML", payload={})
        ]
        
        await cl.Message(
            content="点击下方按钮查看更多信息：",
            actions=actions
        ).send()
        
        return ""
    
    async def show_detailed_analysis(self) -> str:
        """显示详细分析"""
        if not self.current_analysis:
            return "❌ 没有可用的分析结果，请先输入地址进行分析。"
        
        # 生成月度图表
        monthly_chart_json = self.report_agent.generate_monthly_chart(
            self.current_analysis.power_generation
        )
        
        # 发送图表
        await cl.Message(
            content="📊 **月度发电量分布**",
        ).send()
        
        # 这里需要根据Chainlit的具体API来显示图表
        # 暂时发送文字版的月度数据
        monthly_data = self.current_analysis.power_generation.monthly_generation
        months = ['1月', '2月', '3月', '4月', '5月', '6月',
                 '7月', '8月', '9月', '10月', '11月', '12月']
        
        table_content = "| 月份 | 发电量(kWh) |\n|------|------------|\n"
        for i, (month, gen) in enumerate(zip(months, monthly_data)):
            table_content += f"| {month} | {gen:,.0f} |\n"
        
        await cl.Message(content=table_content).send()
        
        # 发送总结
        summary = self.report_agent.generate_summary_text(self.current_analysis)
        await cl.Message(content=summary).send()
        
        return ""
    
    # AI分析功能已移除
    
    async def answer_user_question(self, question: str) -> str:
        """基础问答功能"""
        if not self.current_analysis:
            return "❌ 请先进行光伏系统分析，我才能回答相关问题。"

        # 使用AI顾问的基础问答功能
        answer = await self.ai_advisor.generate_conversational_response(
            question, self.current_analysis
        )

        return answer
    
    async def export_report(self, format_type: str = "html") -> str:
        """导出报告"""
        if not self.current_analysis:
            return "❌ 没有可用的分析结果，请先输入地址进行分析。"

        try:
            if format_type.lower() == "pdf":
                # 生成PDF报告
                await cl.Message(content="📄 正在生成PDF报告，请稍候...").send()

                try:
                    pdf_path = self.report_agent.generate_pdf_report(self.current_analysis)

                    # 发送PDF文件
                    await cl.Message(
                        content="✅ PDF报告生成成功！",
                        elements=[
                            cl.File(
                                name="光伏发电量分析报告.pdf",
                                path=pdf_path,
                                display="inline"
                            )
                        ]
                    ).send()

                    return ""

                except Exception as e:
                    await cl.Message(
                        content=f"❌ PDF生成失败: {str(e)}\n\n💡 建议：\n1. 安装PDF转换库：`pip install weasyprint` 或 `pip install pdfkit`\n2. 或选择HTML格式导出"
                    ).send()

                    # 回退到HTML格式
                    format_type = "html"

            if format_type.lower() == "html":
                # 生成HTML报告
                html_report = self.report_agent.generate_html_report(self.current_analysis)

                # 保存报告文件
                import tempfile
                import os

                with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                    f.write(html_report)
                    report_path = f.name

                # 发送HTML文件
                await cl.Message(
                    content="✅ HTML报告生成成功！",
                    elements=[
                        cl.File(
                            name="光伏发电量分析报告.html",
                            path=report_path,
                            display="inline"
                        )
                    ]
                ).send()

                return ""

        except Exception as e:
            logger.error(f"报告导出失败: {e}")
            return f"❌ 报告导出失败: {str(e)}"
    
    def get_help_message(self) -> str:
        """获取帮助信息"""
        return """
🌟 **光伏参谋AI Copilot使用指南**

**输入方式：**
• 地址：如"北京天安门"、"三河 5kW"
• 坐标：如"39.9042, 116.4074"

**🤖 AI智能功能：**
• "AI分析" - 获得AI驱动的深度投资建议
• 直接提问 - 如"安装成本多少？"、"怎么维护？"

**功能命令：**
• "展开详细" - 查看月度发电量图表
• "AI分析" - AI智能顾问深度分析
• "导出报告" - 生成HTML分析报告
• "帮助" - 显示此帮助信息

**示例输入：**
• 三河 5kW
• 上海市浦东新区 10kW
• 这个系统投资回报怎么样？
• 需要多少钱？

🎯 **AI特色：投资分析、风险评估、技术建议、对话问答**

请输入您想分析的地点开始使用！
        """


# 全局Copilot实例
copilot = PhotovoltaicCopilot()


@cl.on_chat_start
async def start():
    """聊天开始时的欢迎消息"""
    welcome_message = """
🌞 **欢迎使用光伏参谋AI Copilot！**

我是您的专业光伏发电量评估助手，**仅使用真实数据进行分析**：

✨ **100%真实数据源：**
� **地理坐标** - 真实地理编码服务
�️ **气象数据** - NASA POWER卫星观测数据
⚡ **计算模型** - PVLib专业光伏建模库
🤖 **AI分析** - 基于真实数据的智能建议


**开始使用：**
请输入您想评估的地点（地址或经纬度）

**示例：** 北京天安门、三河 5kW、39.9042,116.4074

    """
    
    await cl.Message(content=welcome_message).send()


@cl.on_message
async def main(message: cl.Message):
    """处理用户消息"""
    try:
        response = await copilot.process_user_input(message.content)
        if response:
            await cl.Message(content=response).send()
    except Exception as e:
        logger.error(f"处理消息时出错: {e}")
        await cl.Message(content="❌ 处理请求时出现错误，请稍后重试。").send()


@cl.action_callback("show_details")
async def on_show_details(action):
    """处理展开详细按钮"""
    await copilot.show_detailed_analysis()


# AI分析回调已移除


@cl.action_callback("export_pdf")
async def on_export_pdf(action):
    """处理导出PDF按钮"""
    await copilot.export_report("pdf")


@cl.action_callback("export_html")
async def on_export_html(action):
    """处理导出HTML按钮"""
    await copilot.export_report("html")


if __name__ == "__main__":
    # 配置日志
    logger.add("logs/app.log", rotation="1 day", retention="7 days")
    
    # 启动应用
    import subprocess
    import sys
    subprocess.run([sys.executable, "-m", "chainlit", "run", "app.py", "--port", "8000"])
