# 光伏参谋系统数据流程图

## 🌟 系统整体架构流程

```mermaid
graph TD
    A[用户输入地址] --> B[GeoAgent地理解析]
    B --> C{地址类型判断}
    C -->|坐标格式| D[直接解析坐标]
    C -->|地址文字| E[高德地图API地理编码]

    D --> F[LocationInfo对象]
    E --> F

    F --> G[海拔数据获取流程]
    G --> H[SolarAgent太阳辐射获取]
    H --> I[PVAgent发电量计算]
    I --> J[ReportAgent结果生成]
    J --> K[最终发电量预测结果]

    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style F fill:#fff3e0
```

## 🗺️ 地理位置数据流程

```mermaid
graph TD
    A[用户输入地址] --> B[GeoAgent.parse_location]
    B --> C{输入类型检测}

    C -->|坐标格式| D[parse_coordinates工具函数]
    D --> E[纬度、经度提取]

    C -->|文字地址| F[高德地图API]
    F --> G[地理编码响应]
    G --> H[纬度、经度、省市信息]

    E --> I[海拔获取流程]
    H --> I

    I --> J[Open Elevation API]
    I --> K[高德地图逆编码API]
    I --> L[地形估算算法]

    J -->|成功| M[真实海拔数据]
    K -->|备用| M
    L -->|兜底| M

    M --> N[LocationInfo完整对象]
    N --> O[包含: 地址、纬度、经度、海拔、省市]

    style A fill:#e3f2fd
    style N fill:#f3e5f5
    style O fill:#e8f5e8
```

## 🌞 太阳辐射数据获取流程

```mermaid
graph TD
    A[LocationInfo对象] --> B[SolarAgent.get_solar_data]
    B --> C[NASA POWER API调用]

    C --> D{NASA数据获取}
    D -->|成功| E[NASA POWER卫星数据]
    D -->|失败| F[备用数据源]

    E --> G[T2M温度数据]
    E --> H[ALLSKY_SFC_SW_DWN辐射数据]
    E --> I[RH2M湿度数据]
    E --> J[WS10M风速数据]

    G --> K[T2M温度海拔修正]
    K --> L[_correct_t2m_temperature方法]
    L --> M[海拔修正后的温度]

    H --> N[太阳辐射海拔修正]
    N --> O[_calculate_altitude_factor方法]
    O --> P[大气透过率因子]

    F --> Q[科学估算算法]
    Q --> R[_calculate_daily_irradiance方法]
    R --> S[天文算法 + 大气模型]

    M --> T[SolarData对象列表]
    P --> T
    S --> T

    T --> U[包含: 日期、辐射量、温度]

    style A fill:#e3f2fd
    style E fill:#fff3e0
    style T fill:#e8f5e8
    style U fill:#c8e6c9
```

## 🔬 温度计算详细流程

```mermaid
graph TD
    A[温度计算需求] --> B{数据源类型}

    B -->|NASA真实数据| C[T2M温度数据]
    B -->|科学估算| D[_estimate_temperature方法]
    B -->|天气API数据| E[第三方温度数据]

    C --> F[_correct_t2m_temperature]
    F --> G[海拔修正计算]
    G --> H[修正公式: -(altitude/1000) × 2°C]
    H --> I[NASA修正后温度]

    D --> J[基于纬度的年平均温度]
    D --> K[基于日期的季节变化]
    D --> L[海拔温度修正]
    L --> M[修正公式: -(altitude/100) × 0.6°C]

    J --> N[综合理论温度]
    K --> N
    M --> N

    E --> O[_adjust_temperature_with_real_data]
    O --> P[真实数据海拔调整]

    I --> Q[最终温度数据]
    N --> Q
    P --> Q

    style A fill:#e3f2fd
    style Q fill:#c8e6c9
    style H fill:#ffecb3
    style M fill:#ffecb3
```

## ⚡ 发电量计算核心流程

```mermaid
graph TD
    A[SolarData + PVSystemParams] --> B[PVAgent.calculate_power_generation]

    B --> C[PVWatts模型计算]
    C --> D[基础发电量计算]
    D --> E[公式: 辐射量 × 装机容量 × 系统效率]

    E --> F[温度修正]
    F --> G[温度系数: -0.004%/°C]
    G --> H[修正公式: 基础发电量 × (1 + 温度系数 × (温度-25))]

    H --> I[系统优化分析]
    I --> J[最优倾角计算]
    I --> K[最优方位角计算]

    J --> L[基于纬度的倾角优化]
    K --> M[正南朝向(180°)基准]

    L --> N[优化后发电量]
    M --> N

    N --> O[PowerGeneration对象]
    O --> P[包含: 日发电量、月发电量、年发电量]

    style A fill:#e3f2fd
    style C fill:#fff3e0
    style O fill:#c8e6c9
    style P fill:#a5d6a7
```

## 🎯 参数来源完整映射

```mermaid
graph TD
    subgraph "输入参数"
        A1[用户地址输入]
        A2[光伏系统参数]
    end

    subgraph "地理数据源"
        B1[高德地图API - 坐标]
        B2[Open Elevation API - 海拔]
        B3[地形估算算法 - 海拔备用]
    end

    subgraph "气象数据源"
        C1[NASA POWER API - T2M温度]
        C2[NASA POWER API - 太阳辐射]
        C3[NASA POWER API - 湿度风速]
        C4[科学算法 - 辐射估算]
    end

    subgraph "计算参数"
        D1[海拔因子 - 大气透过率]
        D2[温度修正 - 海拔影响]
        D3[季节变化 - 天文算法]
        D4[纬度影响 - 地理位置]
    end

    subgraph "发电量计算"
        E1[PVWatts模型]
        E2[温度系数修正]
        E3[系统优化算法]
        E4[倾角方位角优化]
    end

    subgraph "最终输出"
        F1[日发电量数据]
        F2[月发电量统计]
        F3[年发电量预测]
        F4[优化建议]
    end

    A1 --> B1
    A1 --> B2
    B1 --> D4
    B2 --> D1
    B2 --> D2

    D4 --> C1
    D4 --> C2
    C1 --> D2
    C2 --> D1

    D1 --> E1
    D2 --> E2
    D3 --> E1
    D4 --> E3

    E1 --> F1
    E2 --> F1
    E3 --> F4
    E4 --> F4

    F1 --> F2
    F2 --> F3

    style A1 fill:#e1f5fe
    style A2 fill:#e1f5fe
    style F1 fill:#c8e6c9
    style F2 fill:#c8e6c9
    style F3 fill:#c8e6c9
    style F4 fill:#c8e6c9
```

## 📊 关键参数详细来源表

| 参数类别     | 具体参数   | 数据来源           | 获取方法      | 备用方案            |
| ------------ | ---------- | ------------------ | ------------- | ------------------- |
| **地理位置** | 纬度、经度 | 高德地图 API       | 地理编码      | 坐标解析            |
|              | 海拔高度   | Open Elevation API | 全球 DEM 数据 | 高德 API + 地形估算 |
|              | 省市信息   | 高德地图 API       | 逆地理编码    | 地址解析            |
| **气象数据** | T2M 温度   | NASA POWER API     | 卫星观测数据  | 科学估算算法        |
|              | 太阳辐射   | NASA POWER API     | 卫星观测数据  | 天文算法计算        |
|              | 湿度风速   | NASA POWER API     | 卫星观测数据  | 默认值              |
| **修正因子** | 海拔因子   | 计算生成           | 大气物理模型  | 固定值 0.75         |
|              | 温度修正   | 计算生成           | 气象学原理    | 纬度估算            |
|              | 季节变化   | 计算生成           | 天文算法      | 正弦函数模拟        |
| **系统参数** | 装机容量   | 用户输入           | 手动指定      | 默认 5kW            |
|              | 倾角方位   | 算法优化           | 纬度计算      | 固定 35°/180°       |
|              | 系统效率   | 预设值             | 行业标准      | 85%默认值           |

## 🔄 数据验证与质量控制

```mermaid
graph TD
    A[原始数据获取] --> B{数据有效性检查}

    B -->|有效| C[数据使用]
    B -->|无效| D[尝试备用数据源]

    D --> E{备用数据可用}
    E -->|是| C
    E -->|否| F[使用科学估算]

    C --> G[参数合理性验证]
    G --> H{参数范围检查}

    H -->|合理| I[应用到计算]
    H -->|异常| J[参数修正/限制]

    J --> I
    F --> I

    I --> K[结果生成]

    style A fill:#e3f2fd
    style K fill:#c8e6c9
    style J fill:#ffecb3
```

这个流程图完整展示了光伏参谋系统中每个参数的来源、数据流向和处理过程，确保了系统的透明性和可追溯性。
