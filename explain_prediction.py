#!/usr/bin/env python3
"""
光伏系统预测原理详解脚本
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams
import pandas as pd
import numpy as np


async def explain_prediction_mechanism():
    """详细解释光伏系统的预测机制"""
    
    print("🔬 光伏参谋系统预测机制详解")
    print("="*60)
    
    print("\n📊 1. 数据来源层")
    print("-" * 30)
    print("🌍 地理数据:")
    print("   • 高德地图API → 精确经纬度坐标")
    print("   • Open Elevation API → 真实海拔数据")
    print("   • 坐标精度: 小数点后6位(约1米精度)")
    
    print("\n🛰️ 气象数据:")
    print("   • NASA POWER API → 卫星观测数据")
    print("   • 数据分辨率: 0.5° × 0.625° (约50公里网格)")
    print("   • 数据历史: 获取过去391天真实观测")
    print("   • 参数包括: 太阳辐射、温度、湿度、风速")
    
    # 演示一个实际案例
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    print("\n🧪 实际案例演示 - 北京市:")
    print("-" * 30)
    
    location = await geo_agent.parse_location("北京市")
    print(f"📍 解析结果: {location.latitude:.6f}°N, {location.longitude:.6f}°E, 海拔{location.altitude}m")
    
    solar_data = await solar_agent.get_solar_data(location)
    print(f"☀️ 获取到 {len(solar_data)} 天的历史气象数据")
    
    # 分析数据质量
    irradiances = [data.irradiance for data in solar_data[:30] if data.irradiance]
    temperatures = [data.temperature for data in solar_data[:30] if data.temperature]
    
    print(f"\n📈 近30天数据样本分析:")
    print(f"   平均太阳辐射: {np.mean(irradiances):.2f} kWh/m²/day")
    print(f"   平均温度: {np.mean(temperatures):.1f}°C")
    print(f"   辐射变化范围: {np.min(irradiances):.2f} - {np.max(irradiances):.2f}")
    
    print("\n🔄 2. 数据处理层")
    print("-" * 30)
    print("🌡️ 海拔温度修正:")
    print("   • 修正公式: T_修正 = T_NASA - (海拔/1000) × 2°C")
    print("   • 物理依据: 海拔每升高1000米,温度下降约2°C")
    print(f"   • 北京修正: {temperatures[0]:.1f}°C - ({location.altitude}/1000)×2 = {temperatures[0] - (location.altitude/1000)*2:.1f}°C")
    
    print("\n☀️ 大气透过率修正:")
    print("   • 修正公式: 因子 = exp(海拔/8500/10) + 修正项")
    print("   • 物理依据: 海拔越高,大气越稀薄,透过率越高")
    altitude_factor = np.exp(location.altitude/8500/10)
    print(f"   • 北京因子: exp({location.altitude}/8500/10) = {altitude_factor:.3f}")
    
    print("\n⏰ 时间序列建模:")
    print("   • 日辐射 → 24小时分布")
    print("   • 正弦函数模拟太阳轨迹")
    print("   • 考虑太阳高度角变化")
    print("   • 区分直射(DNI)和散射(DHI)辐射")
    
    print("\n⚡ 3. 发电量计算层")
    print("-" * 30)
    print("🔬 PVWatts模型(NREL国际标准):")
    print("   • DC功率 = 组件额定功率 × 辐射系数 × 温度系数")
    print("   • AC功率 = DC功率 × 逆变器效率")
    print("   • 温度系数 = 1 + γ × (T电池 - T参考)")
    print("   • γ = -0.004/°C (硅电池标准值)")
    
    # 演示具体计算
    system_params = PVSystemParams(
        capacity_kw=5.0,
        tilt_angle=abs(location.latitude),
        azimuth=180,
        module_efficiency=0.20,
        system_efficiency=0.85,
        performance_ratio=0.80
    )
    
    print(f"\n📐 系统设计参数:")
    print(f"   • 装机容量: {system_params.capacity_kw} kW")
    print(f"   • 最优倾角: {system_params.tilt_angle:.1f}° (等于纬度)")
    print(f"   • 方位角: {system_params.azimuth}° (正南朝向)")
    print(f"   • 组件效率: {system_params.module_efficiency*100}%")
    print(f"   • 系统效率: {system_params.system_efficiency*100}%")
    
    print("\n🧮 计算流程示例(单日):")
    sample_irr = irradiances[0]
    sample_temp = temperatures[0] - (location.altitude/1000)*2
    
    print(f"   1. 输入辐射: {sample_irr:.2f} kWh/m²/day")
    print(f"   2. 修正温度: {sample_temp:.1f}°C")
    
    # 简化计算演示
    temp_coeff = 1 + (-0.004) * (sample_temp - 25)
    basic_gen = sample_irr * system_params.capacity_kw * system_params.system_efficiency
    temp_corrected = basic_gen * temp_coeff
    
    print(f"   3. 温度系数: {temp_coeff:.3f}")
    print(f"   4. 基础发电: {basic_gen:.1f} kWh")
    print(f"   5. 温度修正后: {temp_corrected:.1f} kWh")
    
    print("\n🎯 4. 预测精度验证")
    print("-" * 30)
    print("✅ 数据源可靠性:")
    print("   • NASA POWER: 全球权威气象数据")
    print("   • PVLib: NREL认证的行业标准")
    print("   • 高德地图: 米级精度地理服务")
    
    print("\n📊 预测精度等级:")
    print("   • 年发电量预测: ±5-10% (行业标准)")
    print("   • 月度变化: ±10-15% (季节性因素)")
    print("   • 日度预测: ±15-25% (天气随机性)")
    
    print("\n🔮 5. 预测局限性")
    print("-" * 30)
    print("⚠️ 不确定因素:")
    print("   • 未来天气变化(气候变化影响)")
    print("   • 设备老化衰减(年约0.5-0.8%)")
    print("   • 遮挡和污染影响")
    print("   • 电网政策变化")
    
    print("\n💡 提高精度的方法:")
    print("   • 使用更长历史数据")
    print("   • 考虑微气候差异") 
    print("   • 实时监控校正")
    print("   • 机器学习优化")
    
    print("\n🎯 6. 系统优势")
    print("-" * 30)
    print("🌟 核心特点:")
    print("   • 100%真实数据驱动")
    print("   • 国际标准算法")
    print("   • 多层校验机制")
    print("   • 科学物理建模")
    
    print(f"\n📈 实际计算结果:")
    try:
        power_result = await pv_agent.calculate_power_generation(
            location, solar_data, system_params
        )
        if power_result:
            print(f"   • 年发电量: {power_result.annual_generation:.0f} kWh")
            print(f"   • 日平均: {power_result.annual_generation/365:.1f} kWh/day")
            print(f"   • 容量系数: {power_result.annual_generation/(system_params.capacity_kw*8760)*100:.1f}%")
            
            # 分析可信度
            capacity_factor = power_result.annual_generation/(system_params.capacity_kw*8760)*100
            if 12 <= capacity_factor <= 18:
                confidence = "高"
                reason = "容量系数在北京地区合理范围内"
            elif 10 <= capacity_factor <= 20:
                confidence = "中"
                reason = "容量系数略有偏差但可接受"
            else:
                confidence = "低"
                reason = "容量系数异常,需要进一步验证"
                
            print(f"   • 预测可信度: {confidence} ({reason})")
    except Exception as e:
        print(f"   • 计算错误: {e}")
    
    print("\n" + "="*60)
    print("🎯 总结: 光伏参谋系统基于真实卫星数据和国际标准算法")
    print("    提供科学可靠的发电量预测,精度达到工程应用级别!")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(explain_prediction_mechanism())
