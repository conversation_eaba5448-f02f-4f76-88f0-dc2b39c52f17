#!/usr/bin/env python3
"""
修复月份数据问题的脚本
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams
import pandas as pd
import numpy as np


async def check_data_issues():
    """检查数据月份问题"""
    print("🔍 检查数据月份问题...")
    
    # 初始化agents
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    # 测试北京市数据
    location_info = await geo_agent.parse_location("北京市 5kW")
    solar_data = await solar_agent.get_solar_data(location_info)
    
    print(f"☀️ 原始太阳能数据天数: {len(solar_data)}")
    
    # 检查日期范围
    dates = [data.date for data in solar_data]
    print(f"📅 日期范围: {min(dates)} 到 {max(dates)}")
    
    # 转换为pandas日期进行分析
    pd_dates = pd.to_datetime(dates, format='%Y%m%d')
    print(f"📊 数据跨度: {pd_dates.min().strftime('%Y-%m-%d')} 到 {pd_dates.max().strftime('%Y-%m-%d')}")
    print(f"📊 总天数: {(pd_dates.max() - pd_dates.min()).days + 1}")
    
    # 按月统计
    df_dates = pd.DataFrame({'date': pd_dates})
    monthly_counts = df_dates.groupby([df_dates['date'].dt.year, df_dates['date'].dt.month]).size()
    print(f"\n📅 按月数据统计:")
    for (year, month), count in monthly_counts.items():
        print(f"   {year}-{month:02d}: {count}天")
    
    # 限制为12个月的数据
    print(f"\n🔧 限制数据为完整的12个月...")
    
    # 取最近12个月的数据
    recent_data = solar_data[-365:]  # 取最近365天
    print(f"📅 限制后数据天数: {len(recent_data)}")
    
    # 重新计算发电量
    system_params = PVSystemParams(
        capacity_kw=5.0,
        module_efficiency=0.20,
        system_efficiency=0.85,
        performance_ratio=0.80,
        tilt_angle=abs(location_info.latitude),
        azimuth=180
    )
    
    try:
        power_result = await pv_agent.calculate_power_generation(
            location_info, recent_data, system_params
        )
        
        if power_result:
            print(f"\n📈 修正后发电量结果:")
            print(f"   年发电量: {power_result.annual_generation:.0f} kWh")
            print(f"   月平均: {power_result.monthly_average:.0f} kWh")
            
            # 分析月度发电量变化
            if hasattr(power_result, 'monthly_generation') and power_result.monthly_generation:
                monthly_gen = power_result.monthly_generation
                print(f"\n📊 月度发电量分析 (共{len(monthly_gen)}个月):")
                for i, gen in enumerate(monthly_gen):
                    print(f"   {i+1:2d}月: {gen:.0f} kWh")
                
                # 验证季节性变化是否合理
                winter_avg = np.mean([monthly_gen[0], monthly_gen[1], monthly_gen[11]]) if len(monthly_gen) >= 12 else 0
                summer_avg = np.mean([monthly_gen[5], monthly_gen[6], monthly_gen[7]]) if len(monthly_gen) >= 8 else 0
                
                if winter_avg > 0 and summer_avg > 0:
                    seasonal_ratio = summer_avg / winter_avg
                    print(f"\n🌞 季节性分析:")
                    print(f"   冬季平均: {winter_avg:.0f} kWh")
                    print(f"   夏季平均: {summer_avg:.0f} kWh")
                    print(f"   夏冬比值: {seasonal_ratio:.1f}")
                    
                    if 2.0 <= seasonal_ratio <= 4.0:
                        print("✅ 季节性变化合理（北京地区冬夏发电量差异正常）")
                    else:
                        print("⚠️  季节性变化可能异常")
        
    except Exception as e:
        print(f"❌ 发电量计算失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_data_issues())
