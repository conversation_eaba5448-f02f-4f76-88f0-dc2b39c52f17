#!/usr/bin/env python3
"""
测试海拔对温度和太阳辐射计算的影响
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from loguru import logger

async def test_altitude_impact_on_temperature():
    """测试海拔对温度和太阳辐射计算的影响"""
    logger.info("🧪 开始测试海拔对温度和太阳辐射计算的影响...")
    
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    
    # 测试不同海拔地点
    test_locations = [
        "上海市",      # 低海拔，约16米
        "北京市",      # 中低海拔，约50米
        "昆明市",      # 中海拔，约1935米
        "拉萨市",      # 高海拔，约3660米
    ]
    
    for location_input in test_locations:
        try:
            logger.info(f"\n📍 测试地点: {location_input}")
            
            # 解析地理位置（包含海拔）
            location_info = await geo_agent.parse_location(location_input)
            
            if location_info and location_info.altitude is not None:
                # 获取太阳辐射数据
                solar_data = await solar_agent.get_solar_data(location_info, years=1)
                
                if solar_data and len(solar_data) > 0:
                    # 计算平均太阳辐射和温度
                    avg_irradiance = sum(data.irradiance for data in solar_data) / len(solar_data)
                    avg_temperature = sum(data.temperature for data in solar_data) / len(solar_data)
                    
                    # 计算海拔因子
                    altitude_factor = solar_agent._calculate_altitude_factor(location_info.altitude)
                    
                    # 计算理论温度（无海拔修正）
                    current_day = 200  # 夏季的某一天
                    theoretical_temp_no_altitude = solar_agent._estimate_temperature(
                        location_info.latitude, current_day, altitude=None
                    )
                    theoretical_temp_with_altitude = solar_agent._estimate_temperature(
                        location_info.latitude, current_day, altitude=location_info.altitude
                    )
                    
                    logger.info(f"✅ {location_input} 完整分析:")
                    logger.info(f"   海拔: {location_info.altitude:.0f}米")
                    logger.info(f"   海拔因子: {altitude_factor:.3f}")
                    logger.info(f"   平均日太阳辐射: {avg_irradiance:.3f} kWh/m²/day")
                    logger.info(f"   平均温度: {avg_temperature:.1f}°C")
                    logger.info(f"   理论温度(无海拔修正): {theoretical_temp_no_altitude:.1f}°C")
                    logger.info(f"   理论温度(含海拔修正): {theoretical_temp_with_altitude:.1f}°C")
                    logger.info(f"   海拔温度影响: {theoretical_temp_with_altitude - theoretical_temp_no_altitude:.1f}°C")
                    logger.info(f"   数据天数: {len(solar_data)}天")
                else:
                    logger.error(f"❌ 无法获取{location_input}的太阳辐射数据")
            else:
                logger.error(f"❌ 无法解析{location_input}或获取海拔数据")
                
        except Exception as e:
            logger.error(f"❌ 测试{location_input}时出错: {e}")
    
    logger.info("\n🎯 海拔对温度和太阳辐射影响测试完成")
    logger.info("📊 预期观察：")
    logger.info("   1. 海拔越高，太阳辐射越强（大气稀薄）")
    logger.info("   2. 海拔越高，温度越低（海拔每升高100米，温度降低0.6°C）")
    logger.info("   3. NASA T2M温度会根据海拔进行进一步修正")

if __name__ == "__main__":
    asyncio.run(test_altitude_impact_on_temperature())
