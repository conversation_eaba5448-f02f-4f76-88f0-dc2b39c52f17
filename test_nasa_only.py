#!/usr/bin/env python3
"""
测试纯NASA POWER数据获取
"""

import asyncio
import sys
from loguru import logger

# 添加项目路径
sys.path.append('.')

from agents.solar_agent import SolarAgent
from core.models import LocationInfo


async def test_nasa_power_only():
    """测试纯NASA POWER数据获取"""
    logger.info("🌟 开始测试纯NASA POWER数据获取...")
    
    # 测试位置：盘山（辽宁）
    location = LocationInfo(
        address="盘山",
        latitude=41.2434,
        longitude=121.9964,
        province="辽宁省"
    )
    
    # 创建Solar Agent
    solar_agent = SolarAgent()
    
    # 1. 测试NASA POWER API可用性
    logger.info("\n" + "="*50)
    logger.info("🛰️ 测试NASA POWER API连接")
    logger.info("="*50)
    
    try:
        nasa_data = await solar_agent._get_nasa_power_data(location)
        if nasa_data:
            logger.info("✅ NASA POWER API连接成功")
            logger.info(f"🌞 当前太阳辐射: {nasa_data.get('solar_irradiance')} kWh/m²/day")
            logger.info(f"🌡️ 当前温度: {nasa_data.get('current_temp')}°C")
            logger.info(f"💨 风速: {nasa_data.get('wind_speed')} m/s")
            logger.info(f"💧 湿度: {nasa_data.get('humidity')}%")
            
            historical_data = nasa_data.get('historical_data', {})
            if 'ALLSKY_SFC_SW_DWN' in historical_data:
                data_count = len(historical_data['ALLSKY_SFC_SW_DWN'])
                logger.info(f"📊 历史数据天数: {data_count}天")
        else:
            logger.error("❌ NASA POWER API连接失败")
            return
            
    except Exception as e:
        logger.error(f"❌ NASA POWER API测试失败: {e}")
        return
    
    # 2. 获取完整的太阳辐射数据
    logger.info("\n" + "="*50)
    logger.info("☀️ 获取完整太阳辐射数据")
    logger.info("="*50)
    
    try:
        solar_data = await solar_agent.get_solar_data(location, years=1)
        
        if solar_data and len(solar_data) > 0:
            # 分析数据质量
            total_days = len(solar_data)
            recent_data = solar_data[-7:] if len(solar_data) >= 7 else solar_data
            avg_irradiance = sum(data.irradiance for data in recent_data) / len(recent_data)
            avg_temperature = sum(data.temperature for data in recent_data) / len(recent_data)
            
            logger.info(f"📊 总数据天数: {total_days}天")
            logger.info(f"🌞 最近{len(recent_data)}天平均太阳辐射: {avg_irradiance:.3f} kWh/m²/day")
            logger.info(f"🌡️ 最近{len(recent_data)}天平均温度: {avg_temperature:.1f}°C")
            
            # 显示最近5天的详细数据
            logger.info(f"\n📅 最近{min(5, len(solar_data))}天详细数据:")
            for i, data in enumerate(solar_data[-5:], 1):
                logger.info(f"  {i}. 日期: {data.date}")
                logger.info(f"     太阳辐射: {data.irradiance} kWh/m²/day")
                logger.info(f"     温度: {data.temperature}°C")
            
            # 数据质量评估
            if 3.5 <= avg_irradiance <= 4.5:
                logger.info("✅ 数据质量：符合辽宁地区预期范围 (3.5-4.5 kWh/m²/day)")
            elif avg_irradiance > 4.5:
                logger.warning(f"⚠️ 数据质量：太阳辐射{avg_irradiance:.3f}略高于预期")
            else:
                logger.warning(f"⚠️ 数据质量：太阳辐射{avg_irradiance:.3f}略低于预期")
            
            # 计算年度统计
            all_irradiance = [data.irradiance for data in solar_data]
            yearly_avg = sum(all_irradiance) / len(all_irradiance)
            max_irradiance = max(all_irradiance)
            min_irradiance = min(all_irradiance)
            
            logger.info(f"\n📈 年度统计数据:")
            logger.info(f"   平均太阳辐射: {yearly_avg:.3f} kWh/m²/day")
            logger.info(f"   最大太阳辐射: {max_irradiance:.3f} kWh/m²/day")
            logger.info(f"   最小太阳辐射: {min_irradiance:.3f} kWh/m²/day")
            
        else:
            logger.error("❌ 未能获取太阳辐射数据")
            
    except Exception as e:
        logger.error(f"❌ 获取太阳辐射数据失败: {e}")
    
    # 3. NASA POWER数据准确性分析
    logger.info("\n" + "="*50)
    logger.info("📊 NASA POWER数据准确性分析")
    logger.info("="*50)
    
    try:
        accuracy_analysis = solar_agent.compare_data_accuracy(location)
        
        logger.info(f"📍 位置: {accuracy_analysis['location']}")
        logger.info(f"🗺️ 地区: {accuracy_analysis['region']}")
        logger.info(f"📏 参考范围: {accuracy_analysis['reference_range']}")
        logger.info(f"🎯 最优值: {accuracy_analysis['reference_optimal']}")
        logger.info(f"🛰️ 数据源: {accuracy_analysis['data_source']}")
        
        analysis = accuracy_analysis['accuracy_analysis']
        logger.info(f"🔍 数据质量: {analysis['data_quality']}")
        logger.info(f"📐 准确性等级: {analysis['accuracy_level']}")
        logger.info(f"💡 建议: {analysis['recommendation']}")
        
    except Exception as e:
        logger.error(f"❌ 数据准确性分析失败: {e}")
    
    # 4. NASA POWER API信息
    logger.info("\n" + "="*50)
    logger.info("ℹ️ NASA POWER API信息")
    logger.info("="*50)
    
    logger.info("🆓 API费用: 完全免费")
    logger.info("🔑 API密钥: 不需要注册或申请")
    logger.info("📡 数据来源: NASA卫星和再分析数据")
    logger.info("🌍 覆盖范围: 全球")
    logger.info("📊 数据精度: 0.5° × 0.625° (约50km)")
    logger.info("📅 历史数据: 1981年至今")
    logger.info("🔄 更新频率: 每日更新")
    logger.info("💡 适用场景: 光伏项目可行性研究、能源评估")


if __name__ == "__main__":
    logger.add("test_nasa_only.log", rotation="1 MB")
    asyncio.run(test_nasa_power_only())
