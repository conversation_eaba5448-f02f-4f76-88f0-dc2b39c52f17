#!/usr/bin/env python3
"""
系统测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from agents.report_agent import ReportAgent
from core.models import PVSystemParams
from core.config import settings


async def test_geo_agent():
    """测试地理解析Agent"""
    print("🧪 测试地理解析Agent...")
    
    geo_agent = GeoAgent()
    
    test_addresses = [
        "北京天安门",
        "上海浦东新区",
        "39.9042, 116.4074",
        "广州市"
    ]
    
    for address in test_addresses:
        print(f"  测试地址: {address}")
        location = await geo_agent.parse_location(address)
        if location:
            print(f"    ✅ 解析成功: {location.latitude:.4f}, {location.longitude:.4f}")
        else:
            print(f"    ❌ 解析失败")
    
    print()


async def test_solar_agent():
    """测试太阳辐射数据Agent"""
    print("🧪 测试太阳辐射数据Agent...")
    
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    
    # 使用北京的坐标
    location = await geo_agent.parse_location("北京")
    if location:
        print(f"  获取位置: {location.latitude:.4f}, {location.longitude:.4f}")
        
        # 获取太阳辐射数据（测试用，只获取最近几天）
        print("  正在获取太阳辐射数据...")
        solar_data = await solar_agent.get_solar_data(location, years=0.1)  # 约36天
        
        if solar_data:
            print(f"    ✅ 获取成功: {len(solar_data)}天数据")
            print(f"    平均辐射: {sum(d.irradiance for d in solar_data)/len(solar_data):.2f} kWh/m²/day")
        else:
            print(f"    ❌ 获取失败")
    
    print()


async def test_pv_agent():
    """测试光伏计算Agent"""
    print("🧪 测试光伏计算Agent...")
    
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    # 使用北京的坐标
    location = await geo_agent.parse_location("北京")
    if not location:
        print("    ❌ 地理解析失败")
        return
    
    # 模拟太阳辐射数据（如果API失败）
    from core.models import SolarData
    solar_data = [
        SolarData(date="20231201", irradiance=3.5, temperature=5),
        SolarData(date="20231202", irradiance=4.2, temperature=8),
        SolarData(date="20231203", irradiance=2.8, temperature=3),
    ] * 30  # 模拟90天数据
    
    # 系统参数
    system_params = PVSystemParams(
        capacity_kw=10.0,
        tilt_angle=30.0,
        azimuth=180.0
    )
    
    print(f"  计算10kW系统发电量...")
    generation = await pv_agent.calculate_power_generation(location, solar_data, system_params)
    
    if generation:
        print(f"    ✅ 计算成功")
        print(f"    年发电量: {generation.annual_generation:.0f} kWh")
        print(f"    月均发电: {generation.monthly_average:.0f} kWh")
    else:
        print(f"    ❌ 计算失败")
    
    print()


async def test_report_agent():
    """测试报告生成Agent"""
    print("🧪 测试报告生成Agent...")
    
    from core.models import LocationInfo, PowerGeneration, AnalysisResult
    
    # 模拟数据
    location = LocationInfo(
        address="北京天安门",
        latitude=39.9042,
        longitude=116.4074,
        city="北京",
        country="中国"
    )
    
    system_params = PVSystemParams(capacity_kw=10.0)
    
    power_generation = PowerGeneration(
        daily_generation=[30.5] * 365,
        monthly_generation=[900, 950, 1100, 1200, 1300, 1250, 1200, 1150, 1000, 950, 850, 800],
        annual_generation=11000,
        monthly_average=917
    )
    
    result = AnalysisResult(
        location=location,
        system_params=system_params,
        power_generation=power_generation
    )
    
    report_agent = ReportAgent()
    
    # 测试卡片生成
    card = report_agent.generate_result_card(result)
    print(f"    ✅ 结果卡片生成成功")
    
    # 测试图表生成
    chart_json = report_agent.generate_monthly_chart(power_generation)
    print(f"    ✅ 月度图表生成成功 ({len(chart_json)}字符)")
    
    # 测试HTML报告
    html_report = report_agent.generate_html_report(result)
    print(f"    ✅ HTML报告生成成功 ({len(html_report)}字符)")
    
    print()


async def main():
    """主测试函数"""
    print("🚀 开始系统测试...\n")
    
    try:
        await test_geo_agent()
        await test_solar_agent()
        await test_pv_agent()
        await test_report_agent()
        
        print("✅ 所有测试完成！")
        print("\n🎉 系统基本功能正常，可以启动Chainlit应用:")
        print("   chainlit run app.py")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
