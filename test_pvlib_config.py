#!/usr/bin/env python3
"""
测试PVLib配置的脚本
"""

import pvlib
import pandas as pd
import numpy as np

def test_pvlib_module_config():
    """测试PVLib模块配置"""
    print("=== 测试PVLib模块配置 ===")
    
    # 测试我们的模块参数
    module_parameters = {
        'Technology': 'Mono-c-Si',  # 单晶硅技术
        'Bifacial': 0,  # 非双面
        'STC': 5000,  # 标准条件下功率 5kW
        'PTC': 4300,  # PTC功率
        'A_c': 25.0,  # 组件面积 (m²)
        'Length': 1.65,  # 组件长度 (m)
        'Width': 1.0,   # 组件宽度 (m)
        'N_s': 60,      # 串联电池数
        'I_sc_ref': 9.5,  # 短路电流 (A)
        'V_oc_ref': 37.8, # 开路电压 (V)
        'I_mp_ref': 9.0,  # 最大功率点电流 (A)
        'V_mp_ref': 30.5, # 最大功率点电压 (V)
        'alpha_sc': 0.002146,  # 短路电流温度系数 (A/°C)
        'beta_oc': -0.159068,  # 开路电压温度系数 (V/°C)
        'gamma_r': -0.5072,   # 功率温度系数 (%/°C)
        'NOCT': 45.0,       # 标称工作温度 (°C)
        'Version': 'NRELv1',
        # CEC模型需要的额外参数
        'a_ref': 1.92,      # 修正的理想二极管因子
        'I_L_ref': 9.65,    # 参考光生电流 (A)
        'I_o_ref': 3.25e-10, # 参考饱和电流 (A)
        'R_s': 0.458,       # 串联电阻 (ohm)
        'R_sh_ref': 235.8,  # 参考并联电阻 (ohm)
        'Adjust': 12.5      # 调整参数 (%)
    }
    
    inverter_parameters = {
        'Vac': 220.0,  # 交流额定电压 (V)
        'Paco': 5000,  # 交流额定功率 (W)
        'Pdco': 5500,  # 直流额定功率 (W)
        'Vdco': 350.0,  # 直流额定电压 (V)
        'Pso': 50,  # 待机功耗 (W)
        'C0': -0.000041,  # 系数0
        'C1': -0.000091,  # 系数1
        'C2': 0.000494,   # 系数2
        'C3': -0.013171,  # 系数3
        'Pnt': 0.02       # 夜间待机功耗
    }
    
    try:
        # 创建位置对象
        location = pvlib.location.Location(
            latitude=39.9042,  # 北京纬度
            longitude=116.4074,  # 北京经度
            tz='Asia/Shanghai'
        )
        
        # 创建光伏系统
        system = pvlib.pvsystem.PVSystem(
            surface_tilt=30,
            surface_azimuth=180,
            module_parameters=module_parameters,
            inverter_parameters=inverter_parameters,
            racking_model='open_rack',  # 开放式支架
            module_type='glass_glass'   # 玻璃-玻璃组件
        )
        
        print("✅ PVSystem创建成功！")
        print(f"模块参数数量: {len(module_parameters)}")
        print(f"逆变器参数数量: {len(inverter_parameters)}")
        
        # 创建模型链 - 显式指定DC模型
        mc = pvlib.modelchain.ModelChain(
            system, 
            location,
            dc_model='cec',  # 使用CEC模型
            ac_model='sandia',  # 使用Sandia逆变器模型
            aoi_model='physical',  # 入射角模型
            spectral_model='no_loss',  # 光谱模型
            temperature_model='sapm'  # 温度模型
        )
        print("✅ ModelChain创建成功！")
        
        # 创建测试天气数据
        times = pd.date_range('2024-01-01 06:00', '2024-01-01 18:00', freq='h', tz='Asia/Shanghai')
        
        # 模拟白天的天气数据
        hours = np.arange(len(times))
        ghi = 800 * np.sin(np.pi * hours / (len(times) - 1))  # 白天的辐射曲线
        ghi[ghi < 0] = 0  # 确保非负
        
        weather = pd.DataFrame({
            'ghi': ghi,
            'dhi': ghi * 0.1,  # 散射辐射
            'dni': ghi * 0.9,  # 法向直射辐射  
            'temp_air': np.full(len(times), 25.0),    # 环境温度
            'wind_speed': np.full(len(times), 2.0)    # 风速
        }, index=times)
        
        # 确保所有数值都是有效的
        weather = weather.fillna(0)
        weather[weather < 0] = 0
        
        print("✅ 测试天气数据创建成功！")
        
        # 运行模拟
        mc.run_model(weather)
        print("✅ PVLib模拟运行成功！")
        
        # 检查结果
        if hasattr(mc.results, 'ac') and mc.results.ac is not None:
            daily_generation = mc.results.ac.resample('D').sum() / 1000  # 转换为kWh
            print(f"✅ 计算成功！日发电量: {daily_generation.mean():.2f} kWh")
            return True
        else:
            print("❌ 没有交流输出结果")
            return False
            
    except Exception as e:
        print(f"❌ PVLib配置测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_pvlib_module_config()
    if success:
        print("\n🎉 PVLib配置测试通过！")
    else:
        print("\n❌ PVLib配置存在问题！")
