#!/usr/bin/env python3
"""
测试专业气象数据集成
"""

import asyncio
import sys
from loguru import logger

# 添加项目路径
sys.path.append('.')

from agents.solar_agent import SolarAgent
from core.models import LocationInfo


async def test_professional_apis():
    """测试专业气象API"""
    logger.info("🌟 开始测试专业气象数据集成...")
    
    # 测试位置：盘山（辽宁）
    location = LocationInfo(
        address="盘山",
        latitude=41.2434,
        longitude=121.9964,
        province="辽宁省"
    )
    
    # 创建Solar Agent
    solar_agent = SolarAgent()
    
    # 1. 测试所有专业API的可用性
    logger.info("\n" + "="*50)
    logger.info("🧪 测试专业气象API可用性")
    logger.info("="*50)
    
    api_results = await solar_agent.test_professional_weather_apis(location)
    
    for api_name, result in api_results.items():
        if result['available']:
            logger.info(f"✅ {api_name}: 可用")
            if result['data']:
                for key, value in result['data'].items():
                    logger.info(f"   - {key}: {value}")
        else:
            logger.warning(f"❌ {api_name}: 不可用")
            if result['error']:
                logger.warning(f"   错误: {result['error']}")
    
    # 2. 对比数据准确性
    logger.info("\n" + "="*50)
    logger.info("📊 数据准确性分析")
    logger.info("="*50)
    
    accuracy_comparison = solar_agent.compare_data_accuracy(location, days=30)
    
    logger.info(f"📍 位置: {accuracy_comparison['location']}")
    logger.info(f"🗺️ 地区: {accuracy_comparison['region']}")
    logger.info(f"📏 参考范围: {accuracy_comparison['reference_range']}")
    logger.info(f"🎯 最优值: {accuracy_comparison['reference_optimal']}")
    
    analysis = accuracy_comparison['accuracy_analysis']
    logger.info(f"🧮 计算平均值: {analysis['calculated_average']}")
    logger.info(f"📐 偏差百分比: {analysis['deviation_percentage']}")
    logger.info(f"🔍 准确性状态: {analysis['accuracy_status']}")
    logger.info(f"💡 改进建议: {analysis['recommendation']}")
    
    # 3. 获取实际的太阳辐射数据
    logger.info("\n" + "="*50)
    logger.info("☀️ 获取专业太阳辐射数据")
    logger.info("="*50)
    
    try:
        solar_data = await solar_agent.get_solar_data(location, years=1)
        
        if solar_data and len(solar_data) > 0:
            # 分析最近7天的数据
            recent_data = solar_data[-7:]
            avg_irradiance = sum(data.irradiance for data in recent_data) / len(recent_data)
            avg_temperature = sum(data.temperature for data in recent_data) / len(recent_data)
            
            logger.info(f"📊 成功获取 {len(solar_data)} 天的数据")
            logger.info(f"🌞 最近7天平均太阳辐射: {avg_irradiance:.3f} kWh/m²/day")
            logger.info(f"🌡️ 最近7天平均温度: {avg_temperature:.1f}°C")
            
            # 显示最近3天的详细数据
            logger.info("\n📅 最近3天详细数据:")
            for i, data in enumerate(recent_data[-3:]):
                logger.info(f"  {i+1}. 日期: {data.date}")
                logger.info(f"     太阳辐射: {data.irradiance} kWh/m²/day")
                logger.info(f"     温度: {data.temperature}°C")
            
            # 数据质量评估
            if avg_irradiance >= 3.5 and avg_irradiance <= 4.5:
                logger.info("✅ 数据质量：符合辽宁地区预期范围")
            elif avg_irradiance > 4.5:
                logger.warning("⚠️ 数据质量：偏高，可能需要大气修正")
            else:
                logger.warning("⚠️ 数据质量：偏低，建议使用专业数据源")
                
        else:
            logger.error("❌ 未能获取太阳辐射数据")
            
    except Exception as e:
        logger.error(f"❌ 获取太阳辐射数据失败: {e}")
    
    # 4. API优先级建议
    logger.info("\n" + "="*50)
    logger.info("🏆 API优先级建议")
    logger.info("="*50)
    
    available_apis = [api for api, result in api_results.items() if result['available']]
    
    priority_ranking = {
        'nasa_power': {'rank': 1, 'reason': '最专业的卫星太阳能数据'},
        'pvgis': {'rank': 2, 'reason': '欧盟专业光伏数据库'},
        'cma': {'rank': 3, 'reason': '中国气象局官方数据'},
        'wttr_in': {'rank': 4, 'reason': '免费天气数据，适合基础应用'}
    }
    
    if available_apis:
        logger.info("📈 推荐API使用顺序:")
        sorted_apis = sorted(
            [(api, priority_ranking.get(api, {'rank': 5, 'reason': '其他数据源'})) 
             for api in available_apis],
            key=lambda x: x[1]['rank']
        )
        
        for i, (api, info) in enumerate(sorted_apis, 1):
            logger.info(f"  {i}. {api}: {info['reason']}")
    else:
        logger.warning("⚠️ 当前无可用API，建议:")
        logger.info("  1. 检查网络连接")
        logger.info("  2. 申请NASA POWER API访问（免费）")
        logger.info("  3. 注册中国气象数据网账号")
        logger.info("  4. 考虑使用OpenWeatherMap API key")


if __name__ == "__main__":
    logger.add("test_professional_weather.log", rotation="1 MB")
    asyncio.run(test_professional_apis())
