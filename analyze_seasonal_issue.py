#!/usr/bin/env python3
"""
分析12月vs8月发电量的合理性
检查北京地区的季节性光照特征
"""

import asyncio
import sys
import os
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams
from core.config import settings


async def analyze_seasonal_comparison():
    """分析8月vs12月发电量对比"""
    
    print("🔍 分析北京地区8月vs12月发电量对比")
    print("=" * 60)
    
    # 初始化代理
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    # 解析地址
    location = await geo_agent.parse_location("北京市万橡悦府2期")
    solar_data = await solar_agent.get_solar_data(location)
    
    print(f"📊 分析{len(solar_data)}天的太阳辐射数据")
    
    # 检查8月和12月的原始太阳辐射数据
    august_data = []
    december_data = []
    
    for i, data_point in enumerate(solar_data):
        # 从最近一年开始计算日期
        base_date = datetime.now() - timedelta(days=391-i)
        
        if base_date.month == 8:
            august_data.append(data_point.irradiance)
        elif base_date.month == 12:
            december_data.append(data_point.irradiance)
    
    print(f"\n☀️ 原始太阳辐射数据对比:")
    print("-" * 40)
    
    print(f"8月太阳辐射数据:")
    if august_data:
        print(f"  数据点数: {len(august_data)}")
        print(f"  平均值: {np.mean(august_data):.3f} kWh/m²/day")
        print(f"  最小值: {np.min(august_data):.3f} kWh/m²/day")
        print(f"  最大值: {np.max(august_data):.3f} kWh/m²/day")
    else:
        print(f"  无8月数据")
        
    print(f"\n12月太阳辐射数据:")
    if december_data:
        print(f"  数据点数: {len(december_data)}")
        print(f"  平均值: {np.mean(december_data):.3f} kWh/m²/day")
        print(f"  最小值: {np.min(december_data):.3f} kWh/m²/day")
        print(f"  最大值: {np.max(december_data):.3f} kWh/m²/day")
    else:
        print(f"  无12月数据")
    
    # 计算PVLib发电量
    system_params = PVSystemParams(
        capacity_kw=10.0,
        tilt_angle=30.0,
        azimuth=180.0
    )
    
    power_generation = await pv_agent.calculate_power_generation(
        location, solar_data, system_params
    )
    
    if power_generation:
        monthly_data = power_generation.monthly_generation
        
        print(f"\n⚡ PVLib计算的发电量对比:")
        print("-" * 40)
        print(f"8月发电量: {monthly_data[7]:.0f} kWh")
        print(f"12月发电量: {monthly_data[11]:.0f} kWh")
        print(f"12月比8月多: {monthly_data[11] - monthly_data[7]:.0f} kWh ({((monthly_data[11]/monthly_data[7])-1)*100:+.1f}%)")
        
        # 分析北京地区的理论光照特点
        print(f"\n🌍 北京地区光照理论分析:")
        print("-" * 40)
        print("8月特点:")
        print("  • 夏季，太阳高度角最高")
        print("  • 日照时间长（约14小时）")
        print("  • 但经常有云层、雾霾")
        print("  • 温度高，光伏效率下降")
        
        print("\n12月特点:")
        print("  • 冬季，太阳高度角低")
        print("  • 日照时间短（约9.5小时）")
        print("  • 但天气晴朗，透明度高")
        print("  • 温度低，光伏效率高")
        print("  • 雪反射可增加辐射")
        
        # 检查倾角的影响
        print(f"\n📐 倾角影响分析:")
        print("-" * 30)
        print(f"当前系统倾角: 30°")
        print("• 30°倾角在冬季更有利于接收太阳辐射")
        print("• 冬季太阳高度角低，倾斜面能更好地面向太阳")
        print("• 夏季太阳高度角高，倾斜面反而接收辐射较少")
        
        # 全年月度数据分析
        print(f"\n📈 全年发电量趋势:")
        print("-" * 30)
        months = ['1月', '2月', '3月', '4月', '5月', '6月', 
                 '7月', '8月', '9月', '10月', '11月', '12月']
        
        for i, (month, gen) in enumerate(zip(months, monthly_data)):
            trend = ""
            if i > 0:
                change = gen - monthly_data[i-1]
                trend = f"({change:+.0f})"
            print(f"  {month}: {gen:>6.0f} kWh {trend}")
        
        # 结论
        print(f"\n📝 分析结论:")
        print("-" * 20)
        
        if august_data and december_data:
            aug_avg = np.mean(august_data)
            dec_avg = np.mean(december_data)
            
            if dec_avg > aug_avg:
                print("❌ 异常：12月太阳辐射高于8月，不符合物理规律")
                print("   建议检查：")
                print("   1. 数据时间戳是否正确")
                print("   2. 月份索引是否对应正确")
                print("   3. 是否存在数据混乱")
            else:
                print("✅ 太阳辐射数据正常：8月高于12月")
                print("❓ 但PVLib计算结果异常")
                print("   可能原因：")
                print("   1. 倾角优化在冬季的效果")
                print("   2. 温度系数的影响")
                print("   3. 月度数据累计逻辑问题")
        
        # 建议的合理范围
        print(f"\n💡 北京地区合理发电量范围（10kW系统）:")
        print("-" * 50)
        print("8月（夏季高峰）: 1200-1400 kWh")
        print("12月（冬季低谷）: 600-800 kWh")
        print("合理比例：8月应比12月高50-80%")


if __name__ == "__main__":
    asyncio.run(analyze_seasonal_comparison())
