#!/usr/bin/env python3
"""
直接使用NASA数据测试，绕过我们的转换逻辑
"""

import asyncio
import pandas as pd
import pvlib
import math
from datetime import datetime
from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent

async def test_direct_nasa():
    """直接使用NASA数据，最小化转换逻辑"""
    print("🧪 直接使用NASA数据测试...")
    
    # 获取NASA数据
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    
    location = await geo_agent.parse_location("北京")
    solar_data = await solar_agent.get_solar_data(location)
    
    print(f"获取到{len(solar_data)}天NASA数据")
    
    # 分析几个典型月份的原始数据
    monthly_nasa = {}
    for data in solar_data:
        month = int(data.date[4:6])
        if month not in monthly_nasa:
            monthly_nasa[month] = []
        monthly_nasa[month].append(data.irradiance)
    
    print("\n原始NASA月度数据:")
    for month in [1, 6, 7, 12]:  # 冬夏对比
        if month in monthly_nasa:
            avg = sum(monthly_nasa[month]) / len(monthly_nasa[month])
            print(f"  {month}月: {avg:.2f} kWh/m²/day")
    
    # 选择几个典型日期进行PVLib测试
    test_cases = []
    for data in solar_data:
        month = int(data.date[4:6])
        if month in [1, 6, 7, 12]:  # 只测试关键月份
            test_cases.append(data)
    
    # 按月份分组测试
    monthly_results = {}
    
    for data in test_cases[:20]:  # 只测试前20个样本
        month = int(data.date[4:6])
        
        # 创建该日的小时数据 - 使用最简单的方法
        hourly_data = []
        daily_irr = data.irradiance
        
        for hour in range(24):
            if 8 <= hour <= 16:  # 只在8-16点有辐射
                # 简单的三角形分布
                if hour <= 12:
                    fraction = (hour - 8) / 4  # 8点到12点递增
                else:
                    fraction = (16 - hour) / 4  # 12点到16点递减
                
                # 峰值辐射 = 日总量 × 2 ÷ 8小时
                peak_irr = daily_irr * 1000 * 2 / 8  # W/m²
                ghi = peak_irr * fraction
            else:
                ghi = 0
            
            hourly_data.append({
                'ghi': max(0, ghi),
                'dhi': max(0, ghi * 0.2),  # 20%散射
                'dni': max(0, ghi * 0.8 / 0.7),  # 简化DNI
                'temp_air': data.temperature or 20,
                'wind_speed': 2.0
            })
        
        # 创建时间索引
        date_obj = datetime.strptime(data.date, '%Y%m%d')
        time_index = pd.date_range(
            start=date_obj,
            periods=24,
            freq='h',
            tz='UTC'
        )
        
        weather_df = pd.DataFrame(hourly_data, index=time_index)
        
        # PVLib计算
        site_location = pvlib.location.Location(
            latitude=location.latitude,
            longitude=location.longitude,
            tz='UTC'
        )
        
        module_parameters = {'pdc0': 1000, 'gamma_pdc': -0.004}
        inverter_parameters = {'pdc0': 1000, 'eta_inv_nom': 0.96}
        temperature_model_parameters = {'a': -3.47, 'b': -0.0594, 'deltaT': 3.0}
        
        system = pvlib.pvsystem.PVSystem(
            surface_tilt=30,
            surface_azimuth=180,
            module_parameters=module_parameters,
            inverter_parameters=inverter_parameters,
            temperature_model_parameters=temperature_model_parameters
        )
        
        mc = pvlib.modelchain.ModelChain(
            system, site_location,
            dc_model='pvwatts', ac_model='pvwatts',
            aoi_model='no_loss', spectral_model='no_loss',
            temperature_model='sapm'
        )
        
        mc.run_model(weather_df)
        
        # 计算日发电量
        ac_power = mc.results.ac.fillna(0).clip(lower=0)
        daily_gen = ac_power.sum() / 1000  # kWh
        
        if month not in monthly_results:
            monthly_results[month] = []
        monthly_results[month].append(daily_gen)
        
        print(f"  {data.date} ({month}月): NASA {daily_irr:.2f} kWh/m²/day → PVLib {daily_gen:.3f} kWh")
    
    # 分析月度结果
    print(f"\n月度PVLib结果:")
    for month in sorted(monthly_results.keys()):
        avg_gen = sum(monthly_results[month]) / len(monthly_results[month])
        print(f"  {month}月平均: {avg_gen:.3f} kWh/day")
    
    # 验证季节性
    if 6 in monthly_results and 12 in monthly_results:
        summer_avg = sum(monthly_results[6]) / len(monthly_results[6])
        winter_avg = sum(monthly_results[12]) / len(monthly_results[12])
        
        print(f"\n季节性验证:")
        print(f"  6月(夏): {summer_avg:.3f} kWh/day")
        print(f"  12月(冬): {winter_avg:.3f} kWh/day")
        print(f"  结果: {'✅ 正确' if summer_avg > winter_avg else '❌ 错误'}")

if __name__ == "__main__":
    asyncio.run(test_direct_nasa())
