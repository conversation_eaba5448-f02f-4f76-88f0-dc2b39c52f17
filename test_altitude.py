#!/usr/bin/env python3
"""
测试海拔获取功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from loguru import logger

async def test_altitude_functionality():
    """测试海拔获取功能"""
    logger.info("🧪 开始测试海拔获取功能...")
    
    geo_agent = GeoAgent()
    
    # 测试不同地点的海拔获取
    test_locations = [
        "北京市",
        "拉萨市",
        "上海市",
        "昆明市",
        "乌鲁木齐市",
        "39.9042,116.4074",  # 北京坐标
        "29.5630,91.1720"    # 拉萨坐标
    ]
    
    for location_input in test_locations:
        try:
            logger.info(f"\n📍 测试地点: {location_input}")
            location_info = await geo_agent.parse_location(location_input)
            
            if location_info:
                description = geo_agent.get_location_description(location_info)
                logger.info(f"✅ 解析成功: {description}")
                logger.info(f"   坐标: {location_info.latitude:.4f}, {location_info.longitude:.4f}")
                if location_info.altitude is not None:
                    logger.info(f"   海拔: {location_info.altitude:.0f}米")
                else:
                    logger.warning(f"   ⚠️ 未能获取海拔数据")
            else:
                logger.error(f"❌ 解析失败: {location_input}")
                
        except Exception as e:
            logger.error(f"❌ 测试出错: {e}")
    
    logger.info("\n🎯 海拔获取功能测试完成")

if __name__ == "__main__":
    asyncio.run(test_altitude_functionality())
