#!/usr/bin/env python3
"""
调试辐射数据转换问题
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pvlib

def debug_radiation_conversion():
    """调试辐射数据转换"""
    print("🔍 调试辐射数据转换...")
    
    # 模拟一个简单的测试案例
    # 假设我们有NASA的日辐射量数据：5.0 kWh/m²/day
    daily_irradiation = 5.0  # kWh/m²/day
    
    print(f"📊 NASA日辐射量: {daily_irradiation} kWh/m²/day")
    
    # 方法1：我们当前的转换方法
    print("\n🔧 方法1：当前转换方法")
    
    # 创建一天的小时数据
    times = pd.date_range('2024-06-21 00:00', '2024-06-21 23:00', freq='h', tz='Asia/Shanghai')
    location = pvlib.location.Location(latitude=40.1, longitude=116.3, tz='Asia/Shanghai')
    
    # 计算太阳位置
    solar_position = location.get_solarposition(times)
    
    # 我们当前的方法：基于太阳高度角分配辐射
    hourly_ghi = []
    total_daily_energy = 0
    
    for i, timestamp in enumerate(times):
        solar_elev = solar_position.loc[timestamp, 'apparent_elevation']
        
        if solar_elev > 0:
            # 使用正弦函数分配
            hour_fraction = np.sin(np.radians(solar_elev)) ** 1.2
        else:
            hour_fraction = 0
        
        hourly_ghi.append(hour_fraction)
        total_daily_energy += hour_fraction
    
    # 标准化到日总量
    if total_daily_energy > 0:
        hourly_ghi = [h * daily_irradiation * 1000 / total_daily_energy for h in hourly_ghi]  # W/m²
    
    method1_total = sum(hourly_ghi) / 1000  # kWh/m²
    print(f"  转换后日总量: {method1_total:.3f} kWh/m²/day")
    print(f"  峰值辐射: {max(hourly_ghi):.0f} W/m²")
    print(f"  转换误差: {abs(method1_total - daily_irradiation)/daily_irradiation*100:.1f}%")
    
    # 方法2：使用PVLib的clearsky模型作为参考
    print("\n🌤️ 方法2：PVLib clearsky参考")
    
    clearsky = location.get_clearsky(times)
    clearsky_daily_total = clearsky['ghi'].sum() / 1000  # kWh/m²
    
    print(f"  PVLib clearsky日总量: {clearsky_daily_total:.3f} kWh/m²/day")
    print(f"  PVLib clearsky峰值: {clearsky['ghi'].max():.0f} W/m²")
    
    # 如果我们按clearsky的形状，但缩放到NASA的总量
    if clearsky_daily_total > 0:
        scaled_clearsky = clearsky['ghi'] * (daily_irradiation / clearsky_daily_total)
        scaled_total = scaled_clearsky.sum() / 1000
        print(f"  缩放后日总量: {scaled_total:.3f} kWh/m²/day")
        print(f"  缩放后峰值: {scaled_clearsky.max():.0f} W/m²")
    
    # 方法3：检查我们的DNI/DHI分解是否合理
    print("\n🔄 方法3：检查DNI/DHI分解")
    
    # 使用我们当前的方法
    ghi_values = hourly_ghi
    dhi_values = [g * 0.3 for g in ghi_values]  # 30%散射
    dni_values = [g * 0.7 / max(0.1, np.cos(np.radians(90 - solar_position.loc[times[i], 'apparent_elevation']))) 
                  if solar_position.loc[times[i], 'apparent_elevation'] > 0 else 0 
                  for i, g in enumerate(ghi_values)]
    
    # 验证：DNI*cos(zenith) + DHI 应该 ≈ GHI
    reconstructed_ghi = []
    for i in range(len(times)):
        zenith = 90 - solar_position.loc[times[i], 'apparent_elevation']
        if zenith < 90:
            recon = dni_values[i] * np.cos(np.radians(zenith)) + dhi_values[i]
        else:
            recon = 0
        reconstructed_ghi.append(recon)
    
    original_total = sum(ghi_values) / 1000
    reconstructed_total = sum(reconstructed_ghi) / 1000
    
    print(f"  原始GHI日总量: {original_total:.3f} kWh/m²")
    print(f"  重构GHI日总量: {reconstructed_total:.3f} kWh/m²")
    print(f"  重构误差: {abs(reconstructed_total - original_total)/original_total*100:.1f}%")
    
    # 方法4：直接用PVLib计算一个简单系统看看合理性
    print("\n⚡ 方法4：PVLib简单系统测试")
    
    # 创建一个1kW的简单系统
    simple_weather = pd.DataFrame({
        'ghi': ghi_values,
        'dhi': dhi_values,
        'dni': dni_values,
        'temp_air': [25] * len(times),
        'wind_speed': [2.0] * len(times)
    }, index=times)
    
    # 简单的PVWatts系统
    temperature_model_parameters = pvlib.temperature.TEMPERATURE_MODEL_PARAMETERS['sapm']['open_rack_glass_glass']

    simple_system = pvlib.pvsystem.PVSystem(
        surface_tilt=30,
        surface_azimuth=180,
        module_parameters={'pdc0': 1000, 'gamma_pdc': -0.004},
        inverter_parameters={'pdc0': 1000, 'eta_inv_nom': 0.96},
        temperature_model_parameters=temperature_model_parameters
    )
    
    simple_mc = pvlib.modelchain.ModelChain(
        simple_system, location,
        dc_model='pvwatts',
        ac_model='pvwatts',
        aoi_model='no_loss',
        spectral_model='no_loss',
        temperature_model='sapm'
    )
    
    try:
        simple_mc.run_model(simple_weather)
        daily_generation = simple_mc.results.ac.sum() / 1000  # kWh
        
        print(f"  1kW系统日发电量: {daily_generation:.3f} kWh")
        print(f"  发电量/辐射量比: {daily_generation/daily_irradiation:.3f}")
        print(f"  隐含系统效率: {daily_generation/daily_irradiation*100:.1f}%")
        
        # 理论上，1kW系统在5kWh/m²辐射下，15%效率应该产生：
        theoretical = daily_irradiation * 0.15  # kWh
        print(f"  理论发电量(15%效率): {theoretical:.3f} kWh")
        print(f"  实际/理论比: {daily_generation/theoretical:.3f}")
        
    except Exception as e:
        print(f"  ❌ PVLib计算失败: {e}")

def check_units_and_scaling():
    """检查单位和缩放问题"""
    print("\n📏 检查单位和缩放问题...")
    
    # NASA POWER数据的单位
    print("📊 NASA POWER数据单位:")
    print("  - ALLSKY_SFC_SW_DWN: kWh/m²/day (日累计)")
    print("  - 我们需要转换为: W/m² (瞬时功率)")
    
    # 转换关系
    print("\n🔄 转换关系:")
    print("  - 1 kWh/m²/day = 1000 Wh/m²/day")
    print("  - 如果均匀分布在12小时: 1000/12 ≈ 83 W/m² 平均")
    print("  - 但实际是正弦分布，峰值约为平均值的π/2 ≈ 1.57倍")
    print("  - 所以峰值约为: 83 × 1.57 ≈ 130 W/m²")
    
    # 这解释了为什么我们的峰值辐射偏低！
    print("\n💡 问题发现:")
    print("  - 5 kWh/m²/day 的峰值应该约为 600-800 W/m²")
    print("  - 我们的转换可能过于保守")

if __name__ == "__main__":
    debug_radiation_conversion()
    check_units_and_scaling()
