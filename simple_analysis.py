#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的物理分析脚本：分析太阳能发电的季节性规律
"""

def analyze_monthly_patterns():
    """分析月度发电量的物理合理性"""
    
    print("🔬 光伏发电季节性分析")
    print("=" * 50)
    
    # 北京地区的理论太阳辐射特点 (基于地理和气候常识)
    theoretical_monthly_radiation = {
        1: 2.5,   # 1月 - 冬季最低
        2: 3.2,   # 2月 - 冬季
        3: 4.1,   # 3月 - 春季开始增加
        4: 5.2,   # 4月 - 春季
        5: 6.0,   # 5月 - 春季峰值
        6: 6.5,   # 6月 - 夏季开始
        7: 6.3,   # 7月 - 夏季但有雨季
        8: 5.8,   # 8月 - 夏季但有雨季
        9: 4.8,   # 9月 - 秋季
        10: 3.8,  # 10月 - 秋季
        11: 2.8,  # 11月 - 冬季前期
        12: 2.3   # 12月 - 冬季最低
    }
    
    # 实际测试的发电量数据（从之前的测试获得）
    actual_monthly_generation = {
        1: 1141,  # 1月
        2: 1064,  # 2月  
        3: 1015,  # 3月
        4: 861,   # 4月
        5: 847,   # 5月
        6: 810,   # 6月
        7: 604,   # 7月
        8: 868,   # 8月
        9: 824,   # 9月
        10: 862,  # 10月
        11: 957,  # 11月
        12: 1042  # 12月
    }
    
    print("☀️ 理论vs实际对比分析:")
    print("-" * 50)
    print("月份 | 理论辐射  | 实际发电  | 相对效率")
    print("     |(kWh/m²/d) |  (kWh)   |          ")
    print("-" * 50)
    
    # 计算相对效率（发电量相对于辐射的比值）
    max_theoretical = max(theoretical_monthly_radiation.values())
    max_actual = max(actual_monthly_generation.values())
    
    anomalies = []
    
    for month in range(1, 13):
        theo_rad = theoretical_monthly_radiation[month]
        actual_gen = actual_monthly_generation[month]
        
        # 归一化比较
        theo_norm = theo_rad / max_theoretical
        actual_norm = actual_gen / max_actual
        
        efficiency_ratio = actual_norm / theo_norm if theo_norm > 0 else 0
        
        status = ""
        if efficiency_ratio > 1.3:
            status = " 🔴异常高"
            anomalies.append(f"{month}月效率异常偏高 ({efficiency_ratio:.2f})")
        elif efficiency_ratio < 0.7:
            status = " 🟡偏低"
            anomalies.append(f"{month}月效率偏低 ({efficiency_ratio:.2f})")
        else:
            status = " ✅正常"
            
        print(f" {month:2d}月 |   {theo_rad:4.1f}    |   {actual_gen:4d}   |  {efficiency_ratio:5.2f}{status}")
    
    print("\n🔍 异常情况分析:")
    print("-" * 30)
    
    if anomalies:
        for anomaly in anomalies:
            print(f"⚠️  {anomaly}")
    else:
        print("✅ 未发现明显异常")
    
    print("\n🌡️ 温度效应分析:")
    print("-" * 30)
    
    # 北京的典型月平均温度
    beijing_temps = {
        1: -4, 2: -1, 3: 6, 4: 14, 5: 20, 6: 25,
        7: 27, 8: 26, 9: 21, 10: 14, 11: 5, 12: -2
    }
    
    # 硅电池板温度系数 -0.4%/°C
    temp_coeff = -0.004
    ref_temp = 25
    
    print("考虑温度系数的修正分析:")
    print("月份 | 环境温度 | 温度因子 | 修正效率")
    print("-" * 40)
    
    for month in [1, 7, 8, 12]:  # 重点分析关键月份
        temp = beijing_temps[month]
        temp_factor = 1 + temp_coeff * (temp - ref_temp)
        actual_gen = actual_monthly_generation[month]
        theo_rad = theoretical_monthly_radiation[month]
        
        # 考虑温度修正后的理论发电量
        corrected_gen = theo_rad * temp_factor * 100  # 简化计算
        
        print(f" {month:2d}月 |   {temp:3d}°C   |  {temp_factor:6.3f}  |  实际/理论={actual_gen/corrected_gen:.2f}")
    
    print("\n📊 季节性总结:")
    print("-" * 30)
    
    seasons = {
        "春季(3-5月)": [3, 4, 5],
        "夏季(6-8月)": [6, 7, 8], 
        "秋季(9-11月)": [9, 10, 11],
        "冬季(12-2月)": [12, 1, 2]
    }
    
    for season_name, months in seasons.items():
        total_theo = sum(theoretical_monthly_radiation[m] for m in months)
        total_actual = sum(actual_monthly_generation[m] for m in months)
        avg_theo = total_theo / len(months)
        avg_actual = total_actual / len(months)
        
        print(f"{season_name}: 理论={avg_theo:.1f}, 实际={avg_actual:.0f}kWh")
    
    print("\n🏁 结论:")
    print("-" * 20)
    print("1. 冬季发电量偏高可能原因:")
    print("   - 雪反射增强辐射")
    print("   - 低温提高电池效率")
    print("   - 数据处理算法问题")
    print("\n2. 夏季发电量偏低可能原因:")
    print("   - 高温降低电池效率")
    print("   - 雨季云层遮挡")
    print("   - 大气污染影响")
    print("\n3. 建议进一步验证:")
    print("   - 检查原始气象数据质量")
    print("   - 验证PVLib参数设置")
    print("   - 对比其他地区数据")

if __name__ == "__main__":
    analyze_monthly_patterns()
