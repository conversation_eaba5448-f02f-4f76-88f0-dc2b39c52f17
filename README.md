# 🌞 光伏参谋 AI Copilot

基于极简交互设计的智能光伏发电量评估助手，支持自然语言输入和卡片式结果展示。

## ✨ 功能特性

- 🌍 **智能地理解析**：支持自然语言地址输入，自动转换为精确坐标
- ☀️ **权威气象数据**：集成 NASA POWER API，获取全球太阳辐射数据
- ⚡ **专业光伏建模**：基于 PVLib 进行精确的发电量计算
- 📊 **即时可视化**：生成月度发电量图表和详细分析报告
- 🤖 **多 Agent 协作**：地理解析 → 数据获取 → 发电计算 → 报告生成
- 💬 **极简交互**：纯聊天界面，一句话完成专业评估

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd photovoltaic

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 环境变量已预配置，NASA POWER API无需密钥
# 如需Google Maps API，可在.env中配置
```

### 3. 测试系统

```bash
# 运行系统测试
python test_system.py
```

### 4. 启动应用

```bash
# 启动Chainlit应用
chainlit run app.py
```

## 📁 项目结构

```
photovoltaic/
├── agents/                 # Agent模块
│   ├── geo_agent.py       # 地理解析Agent
│   ├── solar_agent.py     # 太阳辐射数据Agent
│   ├── pv_agent.py        # 光伏发电计算Agent
│   └── report_agent.py    # 可视化报告Agent
├── core/                  # 核心功能
│   ├── models.py          # 数据模型定义
│   ├── config.py          # 配置管理
│   └── utils.py           # 工具函数
├── 光伏参谋md/             # 需求文档
├── app.py                 # Chainlit主应用
├── test_system.py         # 系统测试脚本
├── chainlit.md            # Chainlit欢迎页面
└── requirements.txt       # 依赖列表
```

## 💡 使用说明

### 基础使用

1. 启动应用后，在聊天框输入地址
2. 支持格式：
   - 地址：`北京天安门`、`上海浦东新区`
   - 坐标：`39.9042, 116.4074`
3. 系统自动分析并返回结果卡片
4. 点击"展开详细"查看图表和数据

### 特殊命令

- `展开详细` - 查看月度发电量图表
- `导出报告` - 生成 HTML 分析报告
- `帮助` - 显示使用指南

### 示例对话

```
用户: 北京天安门
AI: 📍 已解析：39.9087,116.3975 ⏳ 评估中...

AI: 📈 年发电量：12,000 kWh
    🔋 月均发电：1,000 kWh
    🎯 建议倾角：28°（+6%增益）
    [展开详细] [导出报告]
```

## 🛠️ 技术栈

- **前端界面**：Chainlit（聊天式交互）
- **后端框架**：Python + AsyncIO
- **光伏建模**：PVLib（可选，有简化模型备用）
- **数据源**：NASA POWER API（免费）
- **地理编码**：Geopy + 内置城市坐标
- **可视化**：Plotly + Matplotlib
- **配置管理**：Pydantic Settings

## 🔧 核心 Agent

### 1. GeoAgent - 地理解析

- 支持自然语言地址解析
- 内置中国主要城市坐标
- 多种坐标格式识别
- 地理编码服务集成

### 2. SolarAgent - 太阳辐射数据

- NASA POWER API 集成
- 历史光照数据获取
- 月度/年度数据聚合
- 峰值日照小时计算

### 3. PVAgent - 光伏发电计算

- PVLib 精确建模（可选）
- 简化计算模型（备用）
- 系统参数优化
- 发电量预测

### 4. ReportAgent - 报告生成

- 结果卡片生成
- 交互式图表
- HTML 报告导出
- 数据可视化

## 🧪 测试

```bash
# 运行完整测试
python test_system.py

# 测试单个模块
python -c "
import asyncio
from agents.geo_agent import GeoAgent
async def test():
    agent = GeoAgent()
    result = await agent.parse_location('北京')
    print(result)
asyncio.run(test())
"
```

## 📊 数据源

- **NASA POWER**：全球太阳辐射数据（1981-至今）
- **内置城市库**：中国主要城市坐标
- **Geopy**：全球地理编码服务
- **PVLib 数据库**：光伏组件和逆变器参数

## 🎯 设计原则

- **极简输入**：单一文本框，自然语言交互
- **即时反馈**：卡片式结果，关键信息突出
- **无多余导航**：纯聊天界面，减少点击
- **轻量展示**：内嵌图表，支持展开收起

## 🚧 开发计划

- [x] 基础 Agent 架构
- [x] 地理解析功能
- [x] 太阳辐射数据获取
- [x] 光伏发电计算
- [x] Chainlit 界面
- [ ] LangGraph 集成
- [ ] 经济效益分析
- [ ] 批量分析功能
- [ ] 地图可视化

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
