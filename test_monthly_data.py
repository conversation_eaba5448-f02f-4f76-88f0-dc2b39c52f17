#!/usr/bin/env python3
"""
测试北京市万橡悦府2期 10kW的月度发电量数据
特别关注第一个月的数据是否异常
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams
from core.config import settings


async def test_monthly_data():
    """测试月度数据"""
    
    print("🔍 测试北京市万橡悦府2期 10kW的月度发电量数据")
    print("=" * 60)
    
    # 初始化代理
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    # 解析地址
    location_input = "北京市万橡悦府2期"
    print(f"📍 解析地址: {location_input}")
    
    location = await geo_agent.parse_location(location_input)
    if not location:
        print("❌ 地址解析失败")
        return
        
    print(f"✅ 地址解析成功: {location.latitude:.6f}, {location.longitude:.6f}")
    print(f"   海拔: {location.altitude}米")
    
    # 获取太阳辐射数据
    print("\n🛰️ 获取NASA POWER太阳辐射数据...")
    solar_data = await solar_agent.get_solar_data(location)
    if not solar_data:
        print("❌ 太阳辐射数据获取失败")
        return
        
    print(f"✅ 数据获取成功，共{len(solar_data)}天数据")
    
    # 设置系统参数
    system_params = PVSystemParams(
        capacity_kw=10.0,
        tilt_angle=settings.default_tilt_angle,
        azimuth=settings.default_azimuth,
        module_efficiency=settings.default_module_efficiency,
        system_efficiency=settings.default_system_efficiency,
        performance_ratio=settings.default_performance_ratio
    )
    
    print(f"\n⚡ 计算10kW光伏系统发电量...")
    print(f"   倾角: {system_params.tilt_angle}°")
    print(f"   方位角: {system_params.azimuth}°")
    
    # 计算发电量
    power_generation = await pv_agent.calculate_power_generation(
        location, solar_data, system_params
    )
    
    if not power_generation:
        print("❌ 发电量计算失败")
        return
    
    # 分析月度数据
    print(f"\n📊 发电量分析结果:")
    print(f"   年发电量: {power_generation.annual_generation:,.0f} kWh")
    print(f"   月均发电: {power_generation.monthly_average:,.0f} kWh")
    
    # 手动计算容量因子
    capacity_factor = (power_generation.annual_generation / (system_params.capacity_kw * 8760)) * 100
    print(f"   容量因子: {capacity_factor:.1f}%")
    
    print(f"\n📅 月度发电量详细分布:")
    print("-" * 50)
    
    months = ['1月', '2月', '3月', '4月', '5月', '6月',
             '7月', '8月', '9月', '10月', '11月', '12月']
    
    monthly_data = power_generation.monthly_generation
    
    # 找出最小和最大值
    min_month_idx = monthly_data.index(min(monthly_data))
    max_month_idx = monthly_data.index(max(monthly_data))
    
    for i, (month, generation) in enumerate(zip(months, monthly_data)):
        status = ""
        if i == min_month_idx:
            status = " ← 最低"
        elif i == max_month_idx:
            status = " ← 最高"
        elif i == 0:  # 1月
            status = " ← 重点关注"
            
        percentage = (generation / power_generation.annual_generation) * 100
        print(f"   {month:>2}: {generation:>6.0f} kWh ({percentage:>4.1f}%){status}")
    
    # 分析第一个月数据
    print(f"\n🔍 第一个月(1月)数据分析:")
    print("-" * 30)
    first_month = monthly_data[0]
    print(f"   1月发电量: {first_month:.0f} kWh")
    print(f"   占年发电量: {(first_month/power_generation.annual_generation)*100:.1f}%")
    print(f"   与年均比较: {(first_month/power_generation.monthly_average-1)*100:+.1f}%")
    
    # 与其他冬季月份比较
    winter_months = [monthly_data[0], monthly_data[1], monthly_data[11]]  # 1月,2月,12月
    winter_avg = sum(winter_months) / len(winter_months)
    print(f"   冬季平均: {winter_avg:.0f} kWh")
    print(f"   1月与冬季平均比: {(first_month/winter_avg-1)*100:+.1f}%")
    
    # 季节性分析
    print(f"\n🌡️ 季节性分析:")
    print("-" * 20)
    
    spring = sum(monthly_data[2:5]) / 3  # 3-5月
    summer = sum(monthly_data[5:8]) / 3  # 6-8月
    autumn = sum(monthly_data[8:11]) / 3  # 9-11月
    winter = sum(winter_months) / 3      # 12,1,2月
    
    seasons = [
        ("春季(3-5月)", spring),
        ("夏季(6-8月)", summer),
        ("秋季(9-11月)", autumn),
        ("冬季(12-2月)", winter)
    ]
    
    for season_name, season_avg in seasons:
        print(f"   {season_name}: {season_avg:>6.0f} kWh/月")
    
    # 数据质量检查
    print(f"\n✅ 数据质量检查:")
    print("-" * 20)
    
    # 检查是否有异常低的值
    if first_month < power_generation.monthly_average * 0.3:
        print("   ⚠️  警告: 1月发电量异常偏低(低于年均30%)")
    else:
        print("   ✅ 1月发电量在合理范围内")
    
    # 检查季节性合理性
    if summer > winter * 1.5:
        print("   ✅ 夏冬季节差异合理")
    else:
        print("   ⚠️  夏冬季节差异可能偏小")
    
    # 检查总和
    total_check = sum(monthly_data)
    if abs(total_check - power_generation.annual_generation) < 1:
        print("   ✅ 月度数据总和与年发电量一致")
    else:
        print(f"   ⚠️  月度数据总和({total_check:.0f})与年发电量({power_generation.annual_generation:.0f})不一致")
    
    print(f"\n📈 结论:")
    print("-" * 10)
    
    if first_month < power_generation.monthly_average * 0.5:
        print("   🔴 1月发电量确实偏低，需要检查计算逻辑")
    elif first_month < power_generation.monthly_average * 0.7:
        print("   🟡 1月发电量较低，但在北方冬季合理范围内")
    else:
        print("   🟢 1月发电量正常，符合北京地区冬季特点")


if __name__ == "__main__":
    asyncio.run(test_monthly_data())
