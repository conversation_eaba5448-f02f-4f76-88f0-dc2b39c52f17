#!/usr/bin/env python3
"""
全面验证光伏计算流程的数据真实性和结果正确性
"""

import asyncio
import sys
import os
import math
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams


async def validate_complete_flow():
    """验证完整的计算流程"""
    print("🔍 开始全面验证光伏计算流程的数据真实性...")
    
    # ===== 1. 地理数据验证 =====
    print("\n📍 [验证1] 地理数据准确性")
    geo_agent = GeoAgent()
    location = await geo_agent.parse_location("北京市万橡悦府2期")
    
    # 验证北京坐标合理性
    beijing_lat_range = (39.4, 41.0)  # 北京纬度范围
    beijing_lon_range = (115.7, 117.4)  # 北京经度范围
    
    lat_valid = beijing_lat_range[0] <= location.latitude <= beijing_lat_range[1]
    lon_valid = beijing_lon_range[0] <= location.longitude <= beijing_lon_range[1]
    
    print(f"  位置: {location.latitude:.4f}°N, {location.longitude:.4f}°E")
    print(f"  纬度验证: {'✅' if lat_valid else '❌'} ({beijing_lat_range})")
    print(f"  经度验证: {'✅' if lon_valid else '❌'} ({beijing_lon_range})")
    print(f"  海拔: {location.altitude}m")
    
    # ===== 2. NASA数据验证 =====
    print("\n🛰️ [验证2] NASA POWER数据真实性")
    solar_agent = SolarAgent()
    solar_data = await solar_agent.get_solar_data(location)
    
    # 验证数据完整性
    print(f"  数据量: {len(solar_data)}天")
    print(f"  时间范围: {solar_data[0].date} 到 {solar_data[-1].date}")
    
    # 验证辐射数据合理性
    irradiances = [data.irradiance for data in solar_data]
    temperatures = [data.temperature for data in solar_data if data.temperature]
    
    irr_min, irr_max = min(irradiances), max(irradiances)
    irr_avg = sum(irradiances) / len(irradiances)
    temp_min, temp_max = min(temperatures), max(temperatures)
    temp_avg = sum(temperatures) / len(temperatures)
    
    print(f"  太阳辐射范围: {irr_min:.2f} - {irr_max:.2f} kWh/m²/day")
    print(f"  太阳辐射平均: {irr_avg:.2f} kWh/m²/day")
    print(f"  温度范围: {temp_min:.1f} - {temp_max:.1f}°C")
    print(f"  温度平均: {temp_avg:.1f}°C")
    
    # 验证北京地区合理性
    beijing_irr_range = (1.0, 8.0)  # 北京太阳辐射合理范围
    beijing_temp_range = (-20, 40)  # 北京温度合理范围
    
    irr_reasonable = beijing_irr_range[0] <= irr_avg <= beijing_irr_range[1]
    temp_reasonable = beijing_temp_range[0] <= temp_avg <= beijing_temp_range[1]
    
    print(f"  辐射合理性: {'✅' if irr_reasonable else '❌'} (期望: {beijing_irr_range})")
    print(f"  温度合理性: {'✅' if temp_reasonable else '❌'} (期望: {beijing_temp_range})")
    
    # 验证季节性
    monthly_irr = {}
    for data in solar_data:
        month = int(data.date[4:6])
        if month not in monthly_irr:
            monthly_irr[month] = []
        monthly_irr[month].append(data.irradiance)
    
    monthly_avg = {m: sum(values)/len(values) for m, values in monthly_irr.items() if values}
    
    if len(monthly_avg) >= 6:
        summer_months = [6, 7, 8]
        winter_months = [12, 1, 2]
        
        summer_avg = sum(monthly_avg.get(m, 0) for m in summer_months if m in monthly_avg) / len([m for m in summer_months if m in monthly_avg])
        winter_avg = sum(monthly_avg.get(m, 0) for m in winter_months if m in monthly_avg) / len([m for m in winter_months if m in monthly_avg])
        
        seasonal_correct = summer_avg > winter_avg
        print(f"  季节性验证: {'✅' if seasonal_correct else '❌'} (夏季{summer_avg:.2f} vs 冬季{winter_avg:.2f})")
    
    # ===== 3. PVLib数据处理验证 =====
    print("\n⚡ [验证3] PVLib数据处理准确性")
    pv_agent = PVAgent()
    
    # 验证气象数据准备
    weather_df = pv_agent._prepare_weather_data(solar_data)
    print(f"  小时数据点: {len(weather_df)}")
    print(f"  预期小时数: {len(solar_data) * 24}")
    
    # 验证GHI数据
    ghi_values = weather_df['ghi'].values
    ghi_nonzero = ghi_values[ghi_values > 0]
    
    print(f"  GHI范围: {ghi_values.min():.1f} - {ghi_values.max():.1f} W/m²")
    print(f"  非零GHI数量: {len(ghi_nonzero)} ({len(ghi_nonzero)/len(ghi_values)*100:.1f}%)")
    
    # 验证单位转换正确性
    # 检查一个典型日的转换
    sample_day_irr = 5.0  # kWh/m²/day
    expected_peak_ghi = sample_day_irr * 1000 / 12  # 约417 W/m²
    
    print(f"  单位转换验证: {sample_day_irr} kWh/m²/day → 峰值约{expected_peak_ghi:.0f} W/m²")
    
    # ===== 4. PVLib计算验证 =====
    print("\n🔬 [验证4] PVLib计算结果验证")
    system_params = PVSystemParams(capacity_kw=10.0)
    
    generation = await pv_agent.calculate_power_generation(location, solar_data, system_params)
    
    if generation:
        print(f"  年发电量: {generation.annual_generation:.0f} kWh")
        print(f"  月均发电: {generation.monthly_average:.0f} kWh")
        
        # 验证发电量合理性
        # 北京10kW系统年发电量合理范围: 8,000-15,000 kWh
        reasonable_range = (8000, 15000)
        generation_reasonable = reasonable_range[0] <= generation.annual_generation <= reasonable_range[1]
        
        print(f"  发电量合理性: {'✅' if generation_reasonable else '❌'} (期望: {reasonable_range})")
        
        # 验证月度季节性
        monthly_gen = generation.monthly_generation
        summer_gen = (monthly_gen[5] + monthly_gen[6] + monthly_gen[7]) / 3  # 6-8月
        winter_gen = (monthly_gen[11] + monthly_gen[0] + monthly_gen[1]) / 3  # 12-2月
        
        seasonal_gen_correct = summer_gen > winter_gen
        print(f"  月度季节性: {'✅' if seasonal_gen_correct else '❌'} (夏季{summer_gen:.0f} vs 冬季{winter_gen:.0f})")
        
        # 验证容量因子
        capacity_factor = generation.annual_generation / (system_params.capacity_kw * 8760)
        reasonable_cf_range = (0.10, 0.25)  # 北京地区合理容量因子
        cf_reasonable = reasonable_cf_range[0] <= capacity_factor <= reasonable_cf_range[1]
        
        print(f"  容量因子: {capacity_factor:.3f} {'✅' if cf_reasonable else '❌'} (期望: {reasonable_cf_range})")
        
        # ===== 5. 物理一致性验证 =====
        print("\n🔬 [验证5] 物理一致性检查")
        
        # 验证辐射-发电量相关性
        monthly_irr_avg = [monthly_avg.get(i+1, 0) for i in range(12)]
        correlation = np.corrcoef(monthly_irr_avg, monthly_gen)[0, 1] if len(monthly_irr_avg) == 12 else 0
        
        print(f"  辐射-发电量相关性: {correlation:.3f} {'✅' if correlation > 0.7 else '❌'} (期望>0.7)")
        
        # 验证温度效应
        # 高温月份发电量应该略有下降（温度系数效应）
        print(f"  温度系数: -0.4%/°C (PVLib标准)")
        
        # ===== 6. 数据源可信度验证 =====
        print("\n📊 [验证6] 数据源可信度")
        print("  NASA POWER: ✅ 美国宇航局权威卫星数据")
        print("  PVLib: ✅ NREL开发的国际标准光伏建模库")
        print("  高德地图: ✅ 国内权威地理编码服务")
        print("  Open Elevation: ✅ 开源海拔数据服务")
        
        # ===== 7. 最终验证结果 =====
        print("\n✅ [验证结果] 数据真实性和结果正确性总结")
        
        all_checks = [
            lat_valid and lon_valid,
            irr_reasonable and temp_reasonable and seasonal_correct,
            len(weather_df) == len(solar_data) * 24,
            generation_reasonable and seasonal_gen_correct and cf_reasonable,
            correlation > 0.7 if correlation else False
        ]
        
        passed_checks = sum(all_checks)
        total_checks = len(all_checks)
        
        print(f"  通过验证: {passed_checks}/{total_checks}")
        
        if passed_checks == total_checks:
            print("  🎉 所有验证通过！数据真实，结果正确！")
        elif passed_checks >= total_checks * 0.8:
            print("  ⚠️ 大部分验证通过，结果基本可信")
        else:
            print("  ❌ 多项验证失败，需要进一步检查")
            
    else:
        print("❌ PVLib计算失败")


if __name__ == "__main__":
    asyncio.run(validate_complete_flow())
