#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入调试：检查原始NASA数据和PVLib计算过程
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime
from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent

async def debug_nasa_data():
    print("🔍 深入调试NASA数据和PVLib计算")
    print("=" * 60)
    
    # 获取原始数据
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    
    location = await geo_agent.parse_location("北京市万橡悦府2期")
    
    # 使用location对象直接获取数据
    solar_data = await solar_agent.get_solar_data(location)
    
    print(f"📊 总数据量: {len(solar_data)} 天")
    print()
    
    # 创建DataFrame分析原始数据
    df = pd.DataFrame({
        'irradiance': [d.irradiance for d in solar_data],
        'temperature': [d.temperature for d in solar_data]
    })
    
    # 添加时间信息
    start_date = datetime(2024, 6, 27)  # NASA数据起始日期
    dates = pd.date_range(start=start_date, periods=len(solar_data), freq='D')
    df.index = dates
    df['month'] = df.index.month
    df['year'] = df.index.year
    
    print("📅 原始NASA数据按月统计:")
    print("-" * 50)
    print("月份 | 数据天数 | 平均辐射 | 平均温度 | 年份分布")
    print("-" * 50)
    
    monthly_stats = df.groupby('month').agg({
        'irradiance': ['count', 'mean'],
        'temperature': 'mean',
        'year': lambda x: list(set(x))
    })
    
    for month in range(1, 13):
        if month in monthly_stats.index:
            count = monthly_stats.loc[month, ('irradiance', 'count')]
            avg_irradiance = monthly_stats.loc[month, ('irradiance', 'mean')]
            avg_temp = monthly_stats.loc[month, ('temperature', 'mean')]
            years = monthly_stats.loc[month, ('year', '<lambda>')]
            print(f" {month:2d}月 |   {count:3d}天   |   {avg_irradiance:6.2f}   |  {avg_temp:6.1f}°C | {years}")
    
    print("\n🔬 对比夏冬季关键月份:")
    print("-" * 40)
    
    key_months = [1, 6, 7, 8, 12]
    for month in key_months:
        if month in df['month'].values:
            month_data = df[df['month'] == month]
            print(f"\n{month}月详细数据:")
            print(f"  数据天数: {len(month_data)}")
            print(f"  辐射范围: {month_data['irradiance'].min():.2f} - {month_data['irradiance'].max():.2f}")
            print(f"  辐射平均: {month_data['irradiance'].mean():.2f}")
            print(f"  温度范围: {month_data['temperature'].min():.1f} - {month_data['temperature'].max():.1f}°C")
            print(f"  温度平均: {month_data['temperature'].mean():.1f}°C")
            print(f"  年份: {sorted(month_data['year'].unique())}")
            
            # 显示前几天的具体数据
            print("  前5天数据样本:")
            for i, (date, row) in enumerate(month_data.head().iterrows()):
                print(f"    {date.strftime('%Y-%m-%d')}: 辐射={row['irradiance']:.2f}, T={row['temperature']:.1f}°C")
    
    print("\n⚠️  可能的数据问题:")
    print("-" * 30)
    
    # 检查异常值
    winter_months = df[df['month'].isin([12, 1, 2])]
    summer_months = df[df['month'].isin([6, 7, 8])]
    
    winter_avg_ghi = winter_months['ghi'].mean()
    summer_avg_ghi = summer_months['ghi'].mean()
    
    print(f"冬季平均GHI: {winter_avg_ghi:.2f} kWh/m²/day")
    print(f"夏季平均GHI: {summer_avg_ghi:.2f} kWh/m²/day")
    print(f"夏冬比值: {summer_avg_ghi/winter_avg_ghi:.2f}")
    
    if summer_avg_ghi < winter_avg_ghi:
        print("🔴 异常：夏季GHI低于冬季！可能的原因:")
        print("   1. 数据时间范围跨越多年，造成季节混乱")
        print("   2. NASA数据质量问题")
        print("   3. 地理位置或数据源配置错误")
    elif summer_avg_ghi / winter_avg_ghi < 1.5:
        print("🟡 注意：夏冬季GHI差异偏小，可能影响发电量计算")
    else:
        print("✅ 夏冬季GHI比值正常")
    
    # 检查数据分布的年份问题
    print(f"\n📊 数据年份分布:")
    year_counts = df['year'].value_counts().sort_index()
    for year, count in year_counts.items():
        print(f"  {year}年: {count}天 ({count/365*100:.1f}%)")
    
    if len(year_counts) > 1:
        print("⚠️  数据跨越多年，可能导致季节性计算错误")

if __name__ == "__main__":
    try:
        asyncio.run(debug_nasa_data())
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
