#!/usr/bin/env python3
"""
测试太阳位置计算是否正确
"""

import asyncio
import pandas as pd
import pvlib
import numpy as np
from datetime import datetime
from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent

async def test_solar_position():
    """测试太阳位置计算"""
    print("🧪 测试太阳位置计算...")
    
    # 获取位置和数据
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    
    location = await geo_agent.parse_location("北京")
    solar_data = await solar_agent.get_solar_data(location)
    
    # 创建PVLib位置对象
    site_location = pvlib.location.Location(
        latitude=location.latitude,
        longitude=location.longitude,
        tz='Asia/Shanghai'  # 北京时间
    )
    
    # 测试几个关键日期
    test_dates = ['20241221', '20250621']  # 冬至和夏至
    
    for date_str in test_dates:
        # 找到对应的NASA数据
        nasa_data = None
        for data in solar_data:
            if data.date == date_str:
                nasa_data = data
                break
        
        if not nasa_data:
            print(f"❌ 找不到{date_str}的NASA数据")
            continue
            
        print(f"\n📅 测试日期: {date_str}")
        print(f"NASA日辐射: {nasa_data.irradiance:.2f} kWh/m²/day")
        
        # 创建该日的24小时时间索引
        date = pd.to_datetime(date_str, format='%Y%m%d')
        time_index = pd.date_range(
            start=date,
            periods=24,
            freq='h',
            tz='Asia/Shanghai'  # 使用北京时间
        )
        
        # 计算太阳位置
        solar_position = site_location.get_solarposition(time_index)
        
        # 分析太阳位置
        elevations = solar_position['apparent_elevation'].values
        positive_mask = elevations > 0
        
        print(f"日出到日落小时数: {positive_mask.sum()}")
        print(f"最大太阳高度角: {elevations.max():.1f}°")
        
        # 计算辐射分配
        solar_fractions = np.maximum(0, np.sin(np.radians(elevations)))
        total_fraction = solar_fractions.sum()
        
        if total_fraction > 0:
            daily_total_energy = nasa_data.irradiance * 1000  # Wh/m²/day
            hourly_ghi = daily_total_energy * solar_fractions / total_fraction
            
            print(f"小时GHI范围: {hourly_ghi.min():.1f} - {hourly_ghi.max():.1f} W/m²")
            print(f"日总辐射验证: {hourly_ghi.sum():.1f} Wh/m²/day (期望: {daily_total_energy:.1f})")
            
            # 检查中午时刻的辐射
            noon_idx = 12
            print(f"中午12点GHI: {hourly_ghi[noon_idx]:.1f} W/m² (太阳高度: {elevations[noon_idx]:.1f}°)")
        else:
            print("❌ 该日无有效太阳辐射")

if __name__ == "__main__":
    asyncio.run(test_solar_position())
