#!/usr/bin/env python3
"""
简单测试PVLib的行为
"""

import pandas as pd
import pvlib
import math
from datetime import datetime, timedelta

def test_pvlib_simple():
    """测试PVLib在不同季节的表现"""
    print("🧪 测试PVLib在不同季节的表现...")
    
    # 北京位置
    location = pvlib.location.Location(latitude=40.1, longitude=116.3, tz='UTC')
    
    # 创建简单的测试数据：夏季和冬季各一天
    test_dates = [
        datetime(2024, 6, 21),  # 夏至
        datetime(2024, 12, 21)  # 冬至
    ]
    
    test_irradiances = [6.0, 2.5]  # 夏季6.0, 冬季2.5 kWh/m²/day
    
    for date, daily_irr in zip(test_dates, test_irradiances):
        print(f"\n📅 测试日期: {date.strftime('%Y-%m-%d')} ({daily_irr} kWh/m²/day)")
        
        # 创建24小时数据
        hourly_data = []
        for hour in range(24):
            if 6 <= hour <= 18:
                # 白天：使用正弦函数分配辐射
                solar_fraction = max(0, math.sin(math.pi * (hour - 6) / 12))
                daily_total_energy = daily_irr * 1000  # Wh/m²/day
                peak_ghi = daily_total_energy / 2.0
                ghi = peak_ghi * solar_fraction
            else:
                ghi = 0
            
            hourly_data.append({
                'ghi': ghi,
                'dhi': ghi * 0.3,  # 简化：30%散射
                'dni': ghi * 0.7 / max(0.1, math.cos(math.radians(45))),  # 简化DNI
                'temp_air': 25.0,
                'wind_speed': 2.0
            })
        
        # 创建时间索引
        time_index = pd.date_range(
            start=date,
            periods=24,
            freq='h',
            tz='UTC'
        )
        
        weather_df = pd.DataFrame(hourly_data, index=time_index)
        
        print(f"  GHI范围: {weather_df['ghi'].min():.1f} - {weather_df['ghi'].max():.1f} W/m²")
        
        # 创建PV系统
        module_parameters = {
            'pdc0': 10000,  # 10kW
            'gamma_pdc': -0.004
        }
        
        inverter_parameters = {
            'pdc0': 10000,
            'eta_inv_nom': 0.96
        }
        
        temperature_model_parameters = {
            'a': -3.47,
            'b': -0.0594,
            'deltaT': 3.0
        }
        
        system = pvlib.pvsystem.PVSystem(
            surface_tilt=30,
            surface_azimuth=180,  # 正南
            module_parameters=module_parameters,
            inverter_parameters=inverter_parameters,
            temperature_model_parameters=temperature_model_parameters
        )
        
        # 创建模型链
        mc = pvlib.modelchain.ModelChain(
            system, 
            location,
            dc_model='pvwatts',
            ac_model='pvwatts',
            aoi_model='no_loss',
            spectral_model='no_loss',
            temperature_model='sapm'
        )
        
        # 运行计算
        mc.run_model(weather_df)
        
        # 分析结果
        ac_power = mc.results.ac.fillna(0).clip(lower=0)
        daily_generation = ac_power.sum() / 1000  # 转换为kWh
        avg_power = ac_power.mean()
        
        print(f"  日发电量: {daily_generation:.2f} kWh")
        print(f"  平均功率: {avg_power:.1f} W")
        print(f"  峰值功率: {ac_power.max():.1f} W")
        
        # 检查太阳位置
        solar_position = location.get_solarposition(time_index)
        max_elevation = solar_position['apparent_elevation'].max()
        print(f"  最大太阳高度角: {max_elevation:.1f}°")

if __name__ == "__main__":
    test_pvlib_simple()
