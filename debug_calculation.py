#!/usr/bin/env python3
"""
调试光伏发电量计算的详细流程
"""

import asyncio
import pandas as pd
from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams

async def debug_calculation():
    """调试完整的计算流程"""
    print("🔍 开始调试光伏发电量计算流程...")
    
    # 1. 地理解析
    geo_agent = GeoAgent()
    location = await geo_agent.parse_location("北京市万橡悦府2期")
    print(f"📍 位置: {location.latitude}, {location.longitude}")
    
    # 2. 获取NASA数据
    solar_agent = SolarAgent()
    solar_data = await solar_agent._get_nasa_power_direct(location)
    
    if not solar_data:
        print("❌ 无法获取NASA数据")
        return
    
    print(f"🛰️ 获取到{len(solar_data)}天NASA数据")
    
    # 3. 分析原始NASA数据的月度分布
    print("\n📊 原始NASA数据月度分析:")
    monthly_nasa = {}
    for data in solar_data:
        month = int(data.date[4:6])
        if month not in monthly_nasa:
            monthly_nasa[month] = []
        monthly_nasa[month].append(data.irradiance)
    
    for month in sorted(monthly_nasa.keys()):
        if monthly_nasa[month]:
            avg = sum(monthly_nasa[month]) / len(monthly_nasa[month])
            print(f"  {month}月: {avg:.2f} kWh/m²/day ({len(monthly_nasa[month])}天)")
    
    # 4. 准备PVLib数据
    pv_agent = PVAgent()
    weather_df = pv_agent._prepare_weather_data(solar_data)
    
    print(f"\n🌤️ PVLib气象数据准备完成: {len(weather_df)}小时")
    print(f"   GHI范围: {weather_df['ghi'].min():.1f} - {weather_df['ghi'].max():.1f} W/m²")
    
    # 5. 分析PVLib数据的月度分布
    print("\n📈 PVLib数据月度分析:")
    weather_df['month'] = weather_df.index.month
    monthly_ghi = weather_df.groupby('month')['ghi'].mean()
    
    for month in sorted(monthly_ghi.index):
        print(f"  {month}月: {monthly_ghi[month]:.1f} W/m² (平均GHI)")
    
    # 6. 运行PVLib计算
    system_params = PVSystemParams(capacity_kw=10.0)
    
    print(f"\n⚡ 开始PVLib发电量计算...")
    try:
        import pvlib
        
        # 创建位置和系统
        site_location = pvlib.location.Location(
            latitude=location.latitude,
            longitude=location.longitude,
            tz='UTC'
        )
        
        module_parameters = {
            'pdc0': 10000,  # 10kW
            'gamma_pdc': -0.004
        }
        
        inverter_parameters = {
            'pdc0': 10000,
            'eta_inv_nom': 0.96
        }

        temperature_model_parameters = {
            'a': -3.47,
            'b': -0.0594,
            'deltaT': 3.0
        }

        system = pvlib.pvsystem.PVSystem(
            surface_tilt=30,
            surface_azimuth=180,
            module_parameters=module_parameters,
            inverter_parameters=inverter_parameters,
            temperature_model_parameters=temperature_model_parameters
        )
        
        mc = pvlib.modelchain.ModelChain(
            system,
            site_location,
            dc_model='pvwatts',
            ac_model='pvwatts',
            aoi_model='no_loss',
            spectral_model='no_loss',
            temperature_model='sapm'
        )
        
        # 运行计算
        mc.run_model(weather_df)
        
        # 分析结果
        ac_power = mc.results.ac.fillna(0).clip(lower=0)
        
        print(f"💡 PVLib计算完成，AC功率范围: {ac_power.min():.1f} - {ac_power.max():.1f} W")
        
        # 月度发电量分析
        print("\n📊 PVLib月度发电量分析:")
        ac_power_df = pd.DataFrame({'ac_power': ac_power})
        ac_power_df['month'] = ac_power_df.index.month
        
        monthly_power = ac_power_df.groupby('month')['ac_power'].agg(['mean', 'sum'])
        
        for month in sorted(monthly_power.index):
            mean_power = monthly_power.loc[month, 'mean']
            total_power = monthly_power.loc[month, 'sum']
            monthly_kwh = total_power / 1000  # 转换为kWh
            print(f"  {month}月: 平均功率{mean_power:.1f}W, 总发电量{monthly_kwh:.0f}kWh")
        
        # 7. 检查数据排序
        print("\n🔍 检查数据时间序列:")
        print(f"   数据开始时间: {weather_df.index[0]}")
        print(f"   数据结束时间: {weather_df.index[-1]}")
        print(f"   时间序列是否单调递增: {weather_df.index.is_monotonic_increasing}")
        
        # 8. 检查是否有异常值
        print("\n⚠️ 异常值检查:")
        zero_ghi_count = (weather_df['ghi'] == 0).sum()
        print(f"   零GHI小时数: {zero_ghi_count} ({zero_ghi_count/len(weather_df)*100:.1f}%)")
        
        high_ghi_count = (weather_df['ghi'] > 1000).sum()
        print(f"   高GHI(>1000)小时数: {high_ghi_count}")
        
    except Exception as e:
        print(f"❌ PVLib计算失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_calculation())
