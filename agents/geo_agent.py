"""
地理解析Agent - 将地址转换为经纬度
"""

import re
import aiohttp
import asyncio
from typing import Optional, Tuple
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut, GeocoderServiceError
from loguru import logger

from core.models import LocationInfo
from core.utils import parse_coordinates


class GeoAgent:
    """地理解析Agent - 支持真实地址解析"""
    
    def __init__(self):
        self.geocoder = Nominatim(user_agent="photovoltaic-advisor")
        
    async def parse_location(self, user_input: str) -> Optional[LocationInfo]:
        """
        解析用户输入的地址 - 只使用高德地图API
        
        Args:
            user_input: 用户输入的地址或坐标
            
        Returns:
            LocationInfo对象或None（无法解析时）
        """
        logger.info(f"开始解析地址: {user_input}")
        
        # 清理输入，提取地址部分
        address_text = self._extract_address(user_input)
        
        # 1. 尝试解析坐标
        coords = parse_coordinates(address_text)
        if coords:
            lat, lon = coords
            logger.info(f"解析到坐标: {lat}, {lon}")
            # 获取海拔高度
            altitude = await self._get_altitude(lat, lon)
            return LocationInfo(
                address=address_text,
                latitude=lat,
                longitude=lon,
                altitude=altitude
            )
        
        # 2. 使用高德地图API（唯一的地理编码服务）
        location_info = await self._geocode_with_amap(address_text)
        if location_info:
            # 获取海拔高度
            altitude = await self._get_altitude(location_info.latitude, location_info.longitude)
            location_info.altitude = altitude
            
        return location_info
    
    def _extract_address(self, user_input: str) -> str:
        """从用户输入中提取地址部分"""
        # 移除功率信息（如5kw, 10kW等）
        address_text = re.sub(r'\d+\s*kw?', '', user_input, flags=re.IGNORECASE)
        address_text = address_text.strip()
        
        # 如果地址太短或太模糊，尝试添加常见后缀
        if len(address_text) <= 2:
            # 对于单个地名，尝试添加常见的地理后缀
            common_suffixes = ['市', '县', '区', '镇', '乡', '村']
            if not any(address_text.endswith(suffix) for suffix in common_suffixes):
                # 尝试多个可能的完整地名
                possible_names = [
                    f"{address_text}市",
                    f"{address_text}县", 
                    f"{address_text}区",
                    f"{address_text}镇"
                ]
                # 返回第一个可能的名称，后续地理编码会验证
                address_text = possible_names[0]
        
        return address_text
    
    async def _geocode_with_amap(self, address: str) -> Optional[LocationInfo]:
        """使用高德地图API进行地理编码（需要申请API Key）"""
        try:
            # 高德地图API Key - 已配置
            api_key = "5a69a1b1adead198deb6b5f1365011fe"  # 光伏参谋系统API Key
            
            if api_key == "YOUR_AMAP_API_KEY":
                logger.error("❌ 高德地图API Key未配置，请按注释说明申请并配置")
                return None
            
            url = "https://restapi.amap.com/v3/geocode/geo"
            params = {
                'address': address,
                'output': 'json',
                'key': api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('status') == '1' and data.get('geocodes'):
                            geocode = data['geocodes'][0]
                            location = geocode.get('location', '').split(',')
                            if len(location) == 2:
                                lon, lat = float(location[0]), float(location[1])
                                logger.info(f"✅ 高德地图解析成功: {address} -> {lat}, {lon}")
                                return LocationInfo(
                                    address=address,
                                    latitude=lat,
                                    longitude=lon,
                                    city=geocode.get('city', ''),
                                    province=geocode.get('province', ''),
                                    country="中国"
                                )
                        else:
                            logger.error(f"❌ 高德地图API返回错误: {data.get('info', '')}")
                            return None
                    else:
                        logger.error(f"❌ 高德地图API请求失败: HTTP {response.status}")
                        return None
        except Exception as e:
            logger.error(f"❌ 高德地图API异常: {e}")
            return None
    
    def get_location_description(self, location: LocationInfo) -> str:
        """获取位置描述文本"""
        parts = []
        if location.city:
            parts.append(location.city)
        if location.province:
            parts.append(location.province)
        if location.country:
            parts.append(location.country)
        
        if parts:
            description = ', '.join(parts)
        else:
            description = f"{location.latitude:.4f}, {location.longitude:.4f}"
            
        # 添加海拔信息
        if location.altitude is not None:
            description += f" (海拔: {location.altitude:.0f}米)"
            
        return description
    
    async def _get_altitude(self, latitude: float, longitude: float) -> Optional[float]:
        """
        获取指定坐标的海拔高度
        使用多个免费API进行海拔查询
        """
        logger.info(f"获取海拔数据: {latitude:.4f}, {longitude:.4f}")
        
        # 方案1: Open Elevation API (免费，开源)
        try:
            altitude = await self._get_altitude_open_elevation(latitude, longitude)
            if altitude is not None:
                logger.info(f"✅ Open Elevation API获取海拔成功: {altitude:.0f}米")
                return altitude
        except Exception as e:
            logger.warning(f"Open Elevation API失败: {e}")
        
        # 方案2: 高德地图逆地理编码API获取海拔
        try:
            altitude = await self._get_altitude_amap(latitude, longitude)
            if altitude is not None:
                logger.info(f"✅ 高德地图获取海拔成功: {altitude:.0f}米")
                return altitude
        except Exception as e:
            logger.warning(f"高德地图海拔API失败: {e}")
        
        # 方案3: 基于DEM数据的估算
        try:
            altitude = self._estimate_altitude_by_region(latitude, longitude)
            if altitude is not None:
                logger.info(f"✅ 基于地形估算海拔: {altitude:.0f}米")
                return altitude
        except Exception as e:
            logger.warning(f"地形估算失败: {e}")
        
        logger.warning(f"无法获取海拔数据，使用默认值")
        return 500.0  # 默认海拔500米
    
    async def _get_altitude_open_elevation(self, latitude: float, longitude: float) -> Optional[float]:
        """使用Open Elevation API获取海拔"""
        try:
            url = "https://api.open-elevation.com/api/v1/lookup"
            data = {
                "locations": [{"latitude": latitude, "longitude": longitude}]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, timeout=15) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('results') and len(result['results']) > 0:
                            elevation = result['results'][0].get('elevation')
                            if elevation is not None:
                                return float(elevation)
        except Exception as e:
            logger.debug(f"Open Elevation API错误: {e}")
            return None
    
    async def _get_altitude_amap(self, latitude: float, longitude: float) -> Optional[float]:
        """使用高德地图逆地理编码获取海拔信息"""
        try:
            api_key = "5a69a1b1adead198deb6b5f1365011fe"
            
            # 高德地图逆地理编码API
            url = "https://restapi.amap.com/v3/geocode/regeo"
            params = {
                'location': f"{longitude},{latitude}",
                'output': 'json',
                'key': api_key,
                'radius': 1000,
                'extensions': 'all'  # 获取详细信息
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('status') == '1' and data.get('regeocode'):
                            # 高德地图API可能不直接提供海拔，但可能在扩展信息中
                            regeocode = data['regeocode']
                            # 尝试从地址组件中推断海拔（基于地形特征）
                            if regeocode.get('addressComponent'):
                                addr_comp = regeocode['addressComponent']
                                # 基于省份和地形特征估算海拔
                                return self._estimate_altitude_from_address(addr_comp, latitude, longitude)
        except Exception as e:
            logger.debug(f"高德地图海拔API错误: {e}")
            return None
    
    def _estimate_altitude_by_region(self, latitude: float, longitude: float) -> Optional[float]:
        """
        基于中国地形特征估算海拔高度
        使用地理知识进行粗略估算
        """
        try:
            # 中国主要地形区域的海拔特征
            if longitude < 90:  # 西部地区
                if latitude > 35:  # 新疆、西藏北部
                    return 2000 + abs(latitude - 35) * 100  # 高原地区
                else:  # 西藏、青海
                    return 3500 + abs(longitude - 90) * 50  # 青藏高原
            elif longitude < 110:  # 中西部
                if latitude > 40:  # 内蒙古、甘肃北部
                    return 1200 + abs(latitude - 40) * 50
                elif latitude > 30:  # 四川、云南、贵州
                    return 1500 + abs(latitude - 30) * 100  # 云贵高原
                else:  # 广西、云南南部
                    return 800 + abs(latitude - 30) * 30
            else:  # 东部地区
                if latitude > 45:  # 东北
                    return 300 + abs(latitude - 45) * 20
                elif latitude > 35:  # 华北
                    return 200 + abs(latitude - 35) * 30
                elif latitude > 25:  # 华中、华东
                    return 100 + abs(latitude - 25) * 20
                else:  # 华南
                    return 50 + abs(latitude - 25) * 10
        except Exception:
            return None
    
    def _estimate_altitude_from_address(self, addr_comp: dict, latitude: float, longitude: float) -> Optional[float]:
        """基于地址组件估算海拔"""
        province = addr_comp.get('province', '')
        city = addr_comp.get('city', '')
        
        # 基于省份的海拔估算
        province_altitudes = {
            '西藏': 4000, '青海': 3000, '新疆': 1500, '云南': 2000,
            '四川': 1200, '甘肃': 1500, '内蒙古': 1000, '贵州': 1100,
            '山西': 800, '陕西': 800, '宁夏': 1200, '河北': 200,
            '北京': 100, '天津': 50, '山东': 150, '河南': 200,
            '江苏': 50, '安徽': 100, '浙江': 200, '江西': 300,
            '福建': 400, '湖北': 300, '湖南': 400, '广东': 200,
            '广西': 300, '海南': 100, '重庆': 800, '上海': 10,
            '辽宁': 200, '吉林': 400, '黑龙江': 300
        }
        
        for prov, alt in province_altitudes.items():
            if prov in province:
                # 在省份基准上添加一些随机变化
                variation = abs(hash(city) % 500) - 250  # -250到+250的变化
                return max(0, alt + variation)
        
        # 如果没有匹配的省份，使用地理位置估算
        return self._estimate_altitude_by_region(latitude, longitude)
