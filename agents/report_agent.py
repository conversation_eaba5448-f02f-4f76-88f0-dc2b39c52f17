"""
报告生成和可视化Agent
"""

import json
import base64
from io import BytesIO
from typing import Dict, Any, Optional
from datetime import datetime

import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder
from loguru import logger

from core.models import AnalysisResult, PowerGeneration
from core.utils import format_number


class ReportAgent:
    """报告生成和可视化Agent"""
    
    def __init__(self):
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def generate_result_card(self, result: AnalysisResult) -> Dict[str, Any]:
        """
        生成结果卡片数据
        
        Args:
            result: 分析结果
            
        Returns:
            卡片数据字典
        """
        logger.info("生成结果卡片")
        
        generation = result.power_generation
        optimization = result.optimization
        
        card_data = {
            "type": "result_card",
            "location": {
                "address": result.location.address,
                "coordinates": f"{result.location.latitude:.4f}, {result.location.longitude:.4f}"
            },
            "system": {
                "capacity": f"{result.system_params.capacity_kw}kW",
                "tilt": f"{result.system_params.tilt_angle}°",
                "azimuth": f"{result.system_params.azimuth}°"
            },
            "generation": {
                "annual": format_number(generation.annual_generation),
                "monthly_avg": format_number(generation.monthly_average),
                "daily_avg": format_number(generation.annual_generation / 365)
            },
            "optimization": None
        }
        
        if optimization:
            card_data["optimization"] = {
                "optimal_tilt": f"{optimization.optimal_tilt:.1f}°",
                "improvement": f"{optimization.generation_improvement:.1f}%"
            }
        
        return card_data
    
    def generate_monthly_chart(self, generation: PowerGeneration) -> str:
        """
        生成月度发电量图表
        
        Args:
            generation: 发电量数据
            
        Returns:
            图表的JSON字符串
        """
        logger.info("生成月度发电量图表")
        
        months = ['1月', '2月', '3月', '4月', '5月', '6月',
                 '7月', '8月', '9月', '10月', '11月', '12月']
        
        fig = go.Figure()
        
        # 添加柱状图
        fig.add_trace(go.Bar(
            x=months,
            y=generation.monthly_generation,
            name='月发电量',
            marker_color='#1f77b4',
            text=[f'{val:.0f}' for val in generation.monthly_generation],
            textposition='auto'
        ))
        
        # 添加平均线
        avg_line = [generation.monthly_average] * 12
        fig.add_trace(go.Scatter(
            x=months,
            y=avg_line,
            mode='lines',
            name='月均发电量',
            line=dict(color='red', dash='dash')
        ))
        
        fig.update_layout(
            title='月度发电量分布',
            xaxis_title='月份',
            yaxis_title='发电量 (kWh)',
            showlegend=True,
            height=400,
            font=dict(family="Arial, sans-serif", size=12)
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def generate_daily_chart(self, generation: PowerGeneration) -> str:
        """
        生成日发电量趋势图
        
        Args:
            generation: 发电量数据
            
        Returns:
            图表的JSON字符串
        """
        logger.info("生成日发电量趋势图")
        
        # 取最近30天的数据
        daily_data = generation.daily_generation[-30:] if len(generation.daily_generation) > 30 else generation.daily_generation
        days = list(range(1, len(daily_data) + 1))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=days,
            y=daily_data,
            mode='lines+markers',
            name='日发电量',
            line=dict(color='#2ca02c', width=2),
            marker=dict(size=4)
        ))
        
        fig.update_layout(
            title='日发电量趋势（最近30天）',
            xaxis_title='天数',
            yaxis_title='发电量 (kWh)',
            height=300,
            font=dict(family="Arial, sans-serif", size=12)
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def generate_comparison_chart(self, result: AnalysisResult) -> Optional[str]:
        """
        生成优化前后对比图
        
        Args:
            result: 分析结果
            
        Returns:
            图表的JSON字符串或None
        """
        if not result.optimization:
            return None
        
        logger.info("生成优化对比图表")
        
        categories = ['当前配置', '优化配置']
        current_gen = result.power_generation.annual_generation
        optimized_gen = current_gen * (1 + result.optimization.generation_improvement / 100)
        
        values = [current_gen, optimized_gen]
        
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=categories,
            y=values,
            marker_color=['#ff7f0e', '#2ca02c'],
            text=[f'{val:.0f} kWh' for val in values],
            textposition='auto'
        ))
        
        fig.update_layout(
            title='优化前后发电量对比',
            yaxis_title='年发电量 (kWh)',
            height=300,
            font=dict(family="Arial, sans-serif", size=12)
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def generate_summary_text(self, result: AnalysisResult) -> str:
        """
        生成文字总结
        
        Args:
            result: 分析结果
            
        Returns:
            总结文本
        """
        generation = result.power_generation
        location_desc = f"{result.location.city or '该地点'}"
        
        summary = f"""
📍 **位置分析**: {location_desc}（{result.location.latitude:.2f}°N, {result.location.longitude:.2f}°E）

⚡ **发电预测**:
• 年发电量: {format_number(generation.annual_generation)} kWh
• 月均发电: {format_number(generation.monthly_average)} kWh  
• 日均发电: {format_number(generation.annual_generation / 365)} kWh

🔧 **系统配置**:
• 装机容量: {result.system_params.capacity_kw} kW
• 安装倾角: {result.system_params.tilt_angle}°
• 朝向方位: {result.system_params.azimuth}°
        """
        
        if result.optimization:
            opt = result.optimization
            summary += f"""
🎯 **优化建议**:
• 建议倾角: {opt.optimal_tilt:.1f}°
• 建议朝向: {opt.optimal_azimuth:.0f}°（正南）
• 发电提升: {opt.generation_improvement:.1f}%
            """
        
        return summary.strip()
    
    def generate_html_report(self, result: AnalysisResult) -> str:
        """
        生成HTML报告
        
        Args:
            result: 分析结果
            
        Returns:
            HTML报告内容
        """
        logger.info("生成HTML报告")
        
        monthly_chart = self.generate_monthly_chart(result.power_generation)
        comparison_chart = self.generate_comparison_chart(result)
        summary = self.generate_summary_text(result)
        
        html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>光伏发电量分析报告</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 30px; }}
        .chart-container {{ margin-bottom: 30px; }}
        .footer {{ text-align: center; color: #666; margin-top: 50px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>光伏发电量分析报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <pre>{summary}</pre>
    </div>
    
    <div class="chart-container">
        <div id="monthly-chart"></div>
    </div>
    
    {f'<div class="chart-container"><div id="comparison-chart"></div></div>' if comparison_chart else ''}
    
    <div class="footer">
        <p>报告由光伏参谋AI Copilot生成</p>
    </div>
    
    <script>
        Plotly.newPlot('monthly-chart', {monthly_chart});
        {f"Plotly.newPlot('comparison-chart', {comparison_chart});" if comparison_chart else ''}
    </script>
</body>
</html>
        """

        logger.info("HTML报告生成完成")
        return html_template

    def generate_pdf_html_content(self, result: AnalysisResult) -> str:
        """
        生成适合PDF的HTML内容（包含静态图表）

        Args:
            result: 分析结果

        Returns:
            HTML内容
        """
        logger.info("生成PDF专用HTML内容")

        # 生成静态图表（matplotlib）
        monthly_chart_base64 = self.generate_static_monthly_chart(result.power_generation)
        comparison_chart_base64 = self.generate_static_comparison_chart(result)
        summary = self.generate_summary_text(result)

        html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>光伏发电量分析报告</title>
    <style>
        body {{ font-family: 'SimSun', Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 30px; }}
        .chart-container {{ text-align: center; margin: 30px 0; }}
        .chart-container img {{ max-width: 100%; height: auto; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: center; }}
        th {{ background-color: #3498db; color: white; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .footer {{ text-align: center; margin-top: 50px; color: #7f8c8d; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>光伏发电量分析报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}</p>
    </div>

    <div class="summary">
        <h2>📊 分析摘要</h2>
        <pre>{summary}</pre>
    </div>

    <div class="section">
        <h2>📈 月度发电量趋势</h2>
        <div class="chart-container">
            <img src="data:image/png;base64,{monthly_chart_base64}" alt="月度发电量图表">
        </div>
    </div>

    <div class="section">
        <h2>📊 性能对比分析</h2>
        <div class="chart-container">
            <img src="data:image/png;base64,{comparison_chart_base64}" alt="性能对比图表">
        </div>
    </div>

    <div class="section">
        <h2>📋 详细数据表</h2>
        <table>
            <thead>
                <tr>
                    <th>月份</th>
                    <th>发电量 (kWh)</th>
                    <th>占年度比例</th>
                </tr>
            </thead>
            <tbody>
"""

        # 添加月度数据表
        months = ['1月', '2月', '3月', '4月', '5月', '6月',
                 '7月', '8月', '9月', '10月', '11月', '12月']

        for i, (month, generation) in enumerate(zip(months, result.power_generation.monthly_generation)):
            percentage = (generation / result.power_generation.annual_generation) * 100
            html_template += f"""
                <tr>
                    <td>{month}</td>
                    <td>{generation:,.0f}</td>
                    <td>{percentage:.1f}%</td>
                </tr>
"""

        html_template += """
            </tbody>
        </table>
    </div>

    <div class="footer">
        <p>本报告由光伏参谋AI Copilot生成 | 基于NASA POWER数据和PVLib专业模型</p>
    </div>
</body>
</html>
        """

        return html_template

    def generate_static_monthly_chart(self, power_generation: PowerGeneration) -> str:
        """生成静态月度图表（base64编码）"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            months = ['1月', '2月', '3月', '4月', '5月', '6月',
                     '7月', '8月', '9月', '10月', '11月', '12月']

            fig, ax = plt.subplots(figsize=(12, 6))
            bars = ax.bar(months, power_generation.monthly_generation,
                         color='#3498db', alpha=0.8, edgecolor='#2980b9', linewidth=1)

            ax.set_title('月度发电量分布', fontsize=16, fontweight='bold', pad=20)
            ax.set_ylabel('发电量 (kWh)', fontsize=12)
            ax.set_xlabel('月份', fontsize=12)

            # 添加数值标签
            for bar, value in zip(bars, power_generation.monthly_generation):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                       f'{value:,.0f}', ha='center', va='bottom', fontsize=10)

            # 美化图表
            ax.grid(True, alpha=0.3, axis='y')
            ax.set_ylim(0, max(power_generation.monthly_generation) * 1.15)
            plt.xticks(rotation=45)
            plt.tight_layout()

            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()

            return image_base64

        except Exception as e:
            logger.error(f"生成静态月度图表失败: {e}")
            return ""

    def generate_static_comparison_chart(self, result: AnalysisResult) -> str:
        """生成静态对比图表（base64编码）"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.use('Agg')

            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建对比数据
            categories = ['年发电量', '月均发电量', '峰值月份', '最低月份']
            actual_values = [
                result.power_generation.annual_generation,
                result.power_generation.monthly_average,
                max(result.power_generation.monthly_generation),
                min(result.power_generation.monthly_generation)
            ]

            # 行业标准参考值（10kW系统）
            industry_values = [13500, 1125, 1500, 800]  # 北京地区典型值

            x = range(len(categories))
            width = 0.35

            fig, ax = plt.subplots(figsize=(10, 6))
            bars1 = ax.bar([i - width/2 for i in x], actual_values, width,
                          label='实际系统', color='#3498db', alpha=0.8)
            bars2 = ax.bar([i + width/2 for i in x], industry_values, width,
                          label='行业标准', color='#e74c3c', alpha=0.8)

            ax.set_title('系统性能对比分析', fontsize=16, fontweight='bold', pad=20)
            ax.set_ylabel('发电量 (kWh)', fontsize=12)
            ax.set_xticks(x)
            ax.set_xticklabels(categories)
            ax.legend()

            # 添加数值标签
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 50,
                           f'{height:,.0f}', ha='center', va='bottom', fontsize=9)

            ax.grid(True, alpha=0.3, axis='y')
            plt.xticks(rotation=15)
            plt.tight_layout()

            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()

            return image_base64

        except Exception as e:
            logger.error(f"生成静态对比图表失败: {e}")
            return ""

    def generate_pdf_report(self, result: AnalysisResult) -> str:
        """
        生成PDF报告

        Args:
            result: 分析结果

        Returns:
            PDF文件路径
        """
        logger.info("生成PDF报告")

        try:
            # 生成PDF专用HTML内容（包含静态图表）
            html_content = self.generate_pdf_html_content(result)

            # 尝试使用weasyprint转换为PDF
            try:
                import weasyprint

                # 创建临时PDF文件
                import tempfile
                pdf_file = tempfile.NamedTemporaryFile(mode='wb', suffix='.pdf', delete=False)
                pdf_path = pdf_file.name
                pdf_file.close()

                # 转换HTML为PDF
                weasyprint.HTML(string=html_content).write_pdf(pdf_path)

                logger.info(f"PDF报告生成成功: {pdf_path}")
                return pdf_path

            except ImportError:
                logger.warning("weasyprint未安装，尝试使用pdfkit")

                # 备选方案：使用pdfkit
                try:
                    import pdfkit

                    # 创建临时PDF文件
                    import tempfile
                    pdf_file = tempfile.NamedTemporaryFile(mode='wb', suffix='.pdf', delete=False)
                    pdf_path = pdf_file.name
                    pdf_file.close()

                    # 配置选项
                    options = {
                        'page-size': 'A4',
                        'margin-top': '0.75in',
                        'margin-right': '0.75in',
                        'margin-bottom': '0.75in',
                        'margin-left': '0.75in',
                        'encoding': "UTF-8",
                        'no-outline': None
                    }

                    # 转换HTML为PDF
                    pdfkit.from_string(html_content, pdf_path, options=options)

                    logger.info(f"PDF报告生成成功: {pdf_path}")
                    return pdf_path

                except ImportError:
                    logger.error("PDF转换库未安装，无法生成PDF")
                    raise Exception("PDF转换库未安装。请安装 weasyprint 或 pdfkit")

        except Exception as e:
            logger.error(f"PDF报告生成失败: {e}")
            raise
