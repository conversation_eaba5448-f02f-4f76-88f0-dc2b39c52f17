"""
简化版太阳辐射数据Agent - 支持真实NASA API和估算备份
"""

import asyncio
import aiohttp
from typing import Optional, List, Dict
from datetime import datetime
import math
from loguru import logger

from core.models import LocationInfo, SolarData


class SolarAgent:
    """简化版太阳辐射数据Agent"""
    
    def __init__(self):
        self.nasa_base_url = "https://power.larc.nasa.gov/api/temporal/daily/point"
    
    async def get_solar_data(self, location: LocationInfo) -> Optional[List[SolarData]]:
        """
        获取太阳辐射数据 - 仅使用真实NASA数据
        
        Args:
            location: 位置信息
            
        Returns:
            太阳辐射数据列表，如果无法获取真实数据则返回None
        """
        logger.info(f"开始获取真实太阳辐射数据: {location.latitude}, {location.longitude}")
        
        try:
            # 只尝试获取真实NASA数据
            real_data = await self._fetch_nasa_data(location)
            if real_data:
                logger.info("✅ 成功获取NASA POWER真实数据")
                return real_data
            else:
                logger.error("❌ 无法获取NASA POWER真实数据")
                return None
        except Exception as e:
            logger.error(f"❌ NASA API失败: {e}")
            return None
    
    async def _fetch_nasa_data(self, location: LocationInfo) -> Optional[List[SolarData]]:
        """从NASA POWER API获取真实数据"""
        try:
            params = {
                'parameters': 'ALLSKY_SFC_SW_DWN,T2M',
                'community': 'RE',
                'longitude': location.longitude,
                'latitude': location.latitude,
                'start': '20230101',
                'end': '20231231',
                'format': 'JSON'
            }
            
            timeout = aiohttp.ClientTimeout(total=10)  # 10秒超时
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(self.nasa_base_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_nasa_response(data)
                    else:
                        logger.warning(f"NASA API状态码: {response.status}")
                        
        except asyncio.TimeoutError:
            logger.warning("NASA API超时")
        except Exception as e:
            logger.warning(f"NASA API请求失败: {e}")
        
        return None
    
    def _parse_nasa_response(self, data: dict) -> Optional[List[SolarData]]:
        """解析NASA API响应"""
        try:
            properties = data.get('properties', {})
            parameter = properties.get('parameter', {})
            
            ghi_data = parameter.get('ALLSKY_SFC_SW_DWN', {})
            temp_data = parameter.get('T2M', {})
            
            if not ghi_data:
                return None
            
            solar_data_list = []
            
            # 每月取一个代表性数据点
            for month in range(1, 13):
                month_ghi = []
                month_temp = []
                
                # 收集该月的所有数据
                for date_str, ghi_value in ghi_data.items():
                    if len(date_str) == 8 and date_str[4:6] == f"{month:02d}":
                        if ghi_value != -999:  # 有效数据
                            month_ghi.append(ghi_value)
                            temp_value = temp_data.get(date_str, 20.0)
                            if temp_value != -999:
                                month_temp.append(temp_value)
                
                # 计算月平均值
                if month_ghi:
                    avg_ghi = sum(month_ghi) / len(month_ghi)
                    avg_temp = sum(month_temp) / len(month_temp) if month_temp else 20.0
                    
                    solar_data = SolarData(
                        date=f"2023{month:02d}15",  # 月中代表
                        irradiance=avg_ghi,
                        temperature=avg_temp
                    )
                    solar_data_list.append(solar_data)
            
            return solar_data_list if len(solar_data_list) >= 6 else None  # 至少6个月数据
            
        except Exception as e:
            logger.error(f"解析NASA数据失败: {e}")
            return None
