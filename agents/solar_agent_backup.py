"""
太阳辐射数据获取Agent - 从OpenWeatherMap API获取气象数据
"""

import requests
import pandas as pd
import math
from datetime import datetime, timedelta
from typing import List, Optional
from loguru import logger

from core.models import SolarData, LocationInfo
from core.config import settings


class SolarAgent:
    """太阳辐射数据Agent"""
    
    def __init__(self):
        # OpenWeatherMap API配置
        self.api_key = "demo_key"  # 使用演示密钥，您可以在config中配置真实密钥
        self.base_url = "http://api.openweathermap.org/data/2.5"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'PhotovoltaicAdvisor/1.0'
        })
    
    async def get_solar_data(self, location: LocationInfo, years: int = 1) -> Optional[List[SolarData]]:
        """
        获取指定位置的太阳辐射数据
        
        Args:
            location: 位置信息
            years: 获取数据的年数（默认1年）
            
        Returns:
            太阳辐射数据列表
        """
        logger.info(f"开始获取太阳辐射数据: {location.latitude}, {location.longitude}")
        
        try:
            # 方案1: 尝试获取当前天气数据
            current_data = await self._get_current_weather(location)
            if current_data:
                logger.info("✅ 获取当前天气数据成功")
                return self._generate_annual_data_from_current(location, current_data)
            
            # 方案2: 如果OpenWeatherMap不可用，使用基于地理位置的太阳辐射估算
            logger.warning("OpenWeatherMap API不可用，使用地理位置太阳辐射估算")
            return self._estimate_solar_data_from_location(location, years)
            
        except Exception as e:
            logger.error(f"❌ 获取太阳辐射数据失败: {e}")
            raise Exception(f"无法获取气象数据: {e}")
    
    async def _get_current_weather(self, location: LocationInfo) -> Optional[dict]:
        """获取当前天气数据"""
        try:
            url = f"{self.base_url}/weather"
            params = {
                'lat': location.latitude,
                'lon': location.longitude,
                'appid': self.api_key,
                'units': 'metric'
            }
            
            response = self.session.get(url, params=params, timeout=10)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                logger.warning("OpenWeatherMap API密钥无效，使用地理估算方法")
                return None
            else:
                logger.warning(f"OpenWeatherMap API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.warning(f"OpenWeatherMap API请求异常: {e}")
            return None
        }
        
        try:
            logger.info("正在请求NASA POWER API...")
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'properties' not in data or 'parameter' not in data['properties']:
                logger.error("API响应格式错误")
                return None
            
            # 解析数据
            solar_data = self._parse_nasa_data(data)
            logger.info(f"成功获取{len(solar_data)}天的太阳辐射数据")
            
            return solar_data
            
        except requests.RequestException as e:
            logger.error(f"NASA POWER API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return None
    
    def _parse_nasa_data(self, data: dict) -> List[SolarData]:
        """解析NASA POWER API返回的数据"""
        solar_data = []
        
        parameters = data['properties']['parameter']
        irradiance_data = parameters.get('ALLSKY_SFC_SW_DWN', {})
        temperature_data = parameters.get('T2M', {})
        
        for date_str, irradiance in irradiance_data.items():
            if irradiance is not None and irradiance > 0:
                temperature = temperature_data.get(date_str)
                
                solar_data.append(SolarData(
                    date=date_str,
                    irradiance=irradiance,
                    temperature=temperature
                ))
        
        return solar_data
    
    def calculate_monthly_averages(self, solar_data: List[SolarData]) -> List[float]:
        """计算月度平均太阳辐射"""
        if not solar_data:
            return []
        
        # 转换为DataFrame便于处理
        df = pd.DataFrame([{
            'date': datetime.strptime(item.date, '%Y%m%d'),
            'irradiance': item.irradiance
        } for item in solar_data])
        
        # 按月分组计算平均值
        df['month'] = df['date'].dt.month
        monthly_avg = df.groupby('month')['irradiance'].mean()
        
        # 确保12个月都有数据
        result = []
        for month in range(1, 13):
            if month in monthly_avg.index:
                result.append(monthly_avg[month])
            else:
                # 如果某月没有数据，使用相邻月份的平均值
                result.append(self._estimate_missing_month(monthly_avg, month))
        
        return result
    
    def _estimate_missing_month(self, monthly_avg: pd.Series, month: int) -> float:
        """估算缺失月份的数据"""
        available_months = monthly_avg.index.tolist()
        if not available_months:
            return 4.0  # 默认值
        
        # 使用最近月份的数据
        closest_month = min(available_months, key=lambda x: abs(x - month))
        return monthly_avg[closest_month]
    
    def get_annual_average(self, solar_data: List[SolarData]) -> float:
        """计算年平均太阳辐射"""
        if not solar_data:
            return 0.0
        
        total_irradiance = sum(item.irradiance for item in solar_data)
        return total_irradiance / len(solar_data)
    
    def get_peak_sun_hours(self, location: LocationInfo) -> float:
        """
        获取峰值日照小时数（简化计算）
        
        Args:
            location: 位置信息
            
        Returns:
            峰值日照小时数
        """
        # 基于纬度的简化估算
        lat = abs(location.latitude)
        
        if lat < 20:
            return 5.5  # 热带地区
        elif lat < 35:
            return 4.5  # 亚热带地区
        elif lat < 50:
            return 3.5  # 温带地区
        else:
            return 2.5  # 高纬度地区
