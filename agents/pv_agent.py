"""
光伏发电模拟Agent - 完全重写版本
基于PVLib最佳实践，确保数据真实准确
"""

import pvlib
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from loguru import logger

from core.models import SolarData, SystemParams, PVResult
from agents.solar_agent import SolarAgent


class PVAgent:
    """光伏发电计算Agent - 重写版本"""
    
    def __init__(self):
        """初始化PV Agent"""
        self.cec_modules = None
        self.cec_inverters = None
        self._load_databases()
    
    def _load_databases(self):
        """加载PVLib数据库"""
        try:
            logger.info("加载PVLib CEC数据库...")
            self.cec_modules = pvlib.pvsystem.retrieve_sam('CECMod')
            self.cec_inverters = pvlib.pvsystem.retrieve_sam('CECInverter')
            logger.info(f"加载完成: {len(self.cec_modules)}个组件, {len(self.cec_inverters)}个逆变器")
        except Exception as e:
            logger.error(f"数据库加载失败: {e}")
            raise
    
    def select_optimal_components(self, target_capacity_kw: float) -> tuple:
        """选择最优的组件和逆变器"""
        logger.info(f"为{target_capacity_kw}kW系统选择最优组件...")
        
        # 选择高效组件 (>20%效率, >400W)
        best_module = None
        best_efficiency = 0
        
        for name, params in self.cec_modules.items():
            stc_power = params.get('STC', 0)
            area = params.get('Area', 2.0)
            
            if stc_power > 400 and area > 0:
                efficiency = stc_power / (area * 1000)  # 转换为效率
                if efficiency > best_efficiency:
                    best_efficiency = efficiency
                    best_module = (name, params)
        
        if not best_module:
            raise Exception("未找到合适的高效组件")
        
        module_name, module_params = best_module
        module_power = module_params['STC']
        
        logger.info(f"选择组件: {module_name}")
        logger.info(f"组件功率: {module_power:.0f}W, 效率: {best_efficiency:.1%}")
        
        # 选择合适的逆变器
        target_power = target_capacity_kw * 1000
        best_inverter = None
        min_diff = float('inf')
        
        for name, params in self.cec_inverters.items():
            paco = params.get('Paco', 0)
            diff = abs(paco - target_power)
            if diff < min_diff:
                min_diff = diff
                best_inverter = (name, params)
        
        if not best_inverter:
            raise Exception("未找到合适的逆变器")
        
        inverter_name, inverter_params = best_inverter
        inverter_power = inverter_params['Paco']
        
        logger.info(f"选择逆变器: {inverter_name}")
        logger.info(f"逆变器功率: {inverter_power:.0f}W")
        
        # 计算组件数量
        modules_needed = int(target_power / module_power)
        actual_dc_power = modules_needed * module_power
        
        logger.info(f"组件数量: {modules_needed}个")
        logger.info(f"实际DC容量: {actual_dc_power:.0f}W")
        logger.info(f"DC/AC比: {actual_dc_power/inverter_power:.2f}")
        
        return (module_name, module_params, modules_needed, 
                inverter_name, inverter_params, actual_dc_power)
    
    def create_realistic_weather(self, location: pvlib.location.Location, 
                               solar_data: List[SolarData]) -> pd.DataFrame:
        """基于NASA数据创建现实气象数据"""
        logger.info("基于NASA数据创建现实气象数据...")
        
        # 创建一年的时间序列
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31, 23, 0)
        times = pd.date_range(start_date, end_date, freq='h', tz=location.tz)
        
        # 使用PVLib的clearsky模型作为基础
        clearsky = location.get_clearsky(times)
        
        # 将NASA日数据转换为字典
        nasa_data = {data.date: data for data in solar_data}
        
        # 提取NASA的月平均辐射量
        nasa_monthly = {}
        for data in solar_data:
            date_obj = datetime.strptime(data.date, '%Y%m%d')
            month = date_obj.month
            if month not in nasa_monthly:
                nasa_monthly[month] = []
            nasa_monthly[month].append(data.irradiance)
        
        # 计算月平均值
        nasa_monthly_avg = {month: np.mean(values) for month, values in nasa_monthly.items()}
        
        # 直接使用NASA数据重构辐射分布，而不是调整clearsky
        adjusted_weather = clearsky.copy()

        # 为每一天分配NASA观测的辐射量
        for date_str, data in nasa_data.items():
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                # 找到该日期的所有小时数据
                day_mask = (times.date == date_obj.date())
                day_times = times[day_mask]

                if len(day_times) > 0:
                    # 计算该日的太阳位置
                    solar_pos = location.get_solarposition(day_times)

                    # 基于太阳高度角分配辐射
                    hourly_fractions = []
                    total_fraction = 0

                    for time in day_times:
                        elev = solar_pos.loc[time, 'apparent_elevation']
                        if elev > 0:
                            fraction = np.sin(np.radians(elev)) ** 1.2
                        else:
                            fraction = 0
                        hourly_fractions.append(fraction)
                        total_fraction += fraction

                    # 标准化并分配NASA的日总辐射量
                    if total_fraction > 0:
                        daily_total_wh = data.irradiance * 1000  # kWh/m²/day -> Wh/m²/day
                        for i, time in enumerate(day_times):
                            hourly_ghi = hourly_fractions[i] * daily_total_wh / total_fraction
                            adjusted_weather.loc[time, 'ghi'] = hourly_ghi
                            adjusted_weather.loc[time, 'dhi'] = hourly_ghi * 0.3  # 30%散射

                            # 计算DNI
                            zenith = 90 - solar_pos.loc[time, 'apparent_elevation']
                            if zenith < 85:  # 太阳高度角>5度
                                dni = hourly_ghi * 0.7 / np.cos(np.radians(zenith))
                            else:
                                dni = 0
                            adjusted_weather.loc[time, 'dni'] = dni

            except Exception as e:
                logger.warning(f"处理日期{date_str}时出错: {e}")
                continue
        
        # 添加温度和风速数据
        adjusted_weather['temp_air'] = 15 + 15 * np.sin(2 * np.pi * (times.dayofyear - 80) / 365)
        adjusted_weather['wind_speed'] = 2.0 + np.random.normal(0, 0.5, len(times))
        adjusted_weather['wind_speed'] = np.clip(adjusted_weather['wind_speed'], 0.5, 10)
        
        annual_ghi = adjusted_weather['ghi'].sum() / 1000  # kWh/m²
        logger.info(f"年GHI总量: {annual_ghi:.0f} kWh/m²")
        
        return adjusted_weather
    
    def create_pv_system(self, system_params: SystemParams) -> pvlib.pvsystem.PVSystem:
        """创建PV系统"""
        logger.info("创建PV系统...")
        
        # 选择最优组件
        (module_name, module_params, modules_needed, 
         inverter_name, inverter_params, actual_dc_power) = self.select_optimal_components(
            system_params.capacity_kw)
        
        # 创建PV系统
        system = pvlib.pvsystem.PVSystem(
            surface_tilt=system_params.tilt_angle,
            surface_azimuth=system_params.azimuth,
            module_parameters=module_params,
            inverter_parameters=inverter_params,
            temperature_model_parameters=pvlib.temperature.TEMPERATURE_MODEL_PARAMETERS['sapm']['open_rack_glass_glass'],
            modules_per_string=modules_needed,
            strings_per_inverter=1
        )
        
        # 存储实际容量信息
        system._actual_dc_capacity = actual_dc_power
        system._module_name = module_name
        system._inverter_name = inverter_name
        
        return system
    
    async def calculate_pv_generation(self, 
                                    system_params: SystemParams, 
                                    solar_data: List[SolarData]) -> PVResult:
        """计算光伏发电量 - 重写版本"""
        try:
            logger.info("开始PVLib专业计算 - 使用CEC模型...")
            
            # 创建位置对象
            site_location = pvlib.location.Location(
                latitude=system_params.latitude,
                longitude=system_params.longitude,
                tz='Asia/Shanghai'
            )
            
            # 创建现实气象数据
            weather_data = self.create_realistic_weather(site_location, solar_data)
            
            # 创建PV系统
            system = self.create_pv_system(system_params)
            
            # 创建ModelChain - 使用最准确的模型
            mc = pvlib.modelchain.ModelChain(
                system, 
                site_location,
                dc_model='cec',           # CEC DC模型 - 最准确
                ac_model='sandia',        # Sandia AC模型 - 最准确
                aoi_model='physical',     # 物理AOI模型
                spectral_model='no_loss', # 暂不考虑光谱损失
                temperature_model='sapm'  # SAPM温度模型
            )
            
            logger.info("开始仿真计算...")
            
            # 运行仿真
            mc.run_model(weather_data)
            
            # 处理结果
            dc_power = mc.results.dc
            ac_power = mc.results.ac
            
            # 处理多数组情况
            if isinstance(dc_power, tuple):
                dc_power = dc_power[0]
            if isinstance(ac_power, tuple):
                ac_power = ac_power[0]
            
            # 提取功率值
            if hasattr(dc_power, 'p_mp'):
                dc_values = dc_power['p_mp']
            else:
                dc_values = dc_power
            
            ac_values = ac_power
            
            # 计算月度发电量
            monthly_generation = ac_values.resample('ME').sum() / 1000  # kWh
            annual_generation = ac_values.sum() / 1000  # kWh
            
            # 计算容量因子
            system_capacity = system._actual_dc_capacity / 1000  # kW
            capacity_factor = annual_generation / (system_capacity * 8760)
            
            # 构建月度数据
            monthly_data = []
            for month_end, generation in monthly_generation.items():
                monthly_data.append({
                    'month': month_end.month,
                    'generation_kwh': float(generation)
                })
            
            logger.info(f"计算完成 - 年发电量: {annual_generation:.0f} kWh")
            logger.info(f"容量因子: {capacity_factor:.1%}")
            
            return PVResult(
                annual_generation_kwh=float(annual_generation),
                monthly_generation=monthly_data,
                capacity_factor=float(capacity_factor),
                system_efficiency=float(capacity_factor * 8760 / (weather_data['ghi'].sum() / 1000)),
                peak_power_kw=float(ac_values.max() / 1000),
                metadata={
                    'module_name': system._module_name,
                    'inverter_name': system._inverter_name,
                    'actual_dc_capacity_kw': float(system._actual_dc_capacity / 1000),
                    'dc_ac_ratio': float(system._actual_dc_capacity / system.inverter_parameters['Paco']),
                    'annual_ghi_kwh_m2': float(weather_data['ghi'].sum() / 1000)
                }
            )
            
        except Exception as e:
            logger.error(f"PV计算失败: {e}")
            raise
