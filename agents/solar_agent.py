"""
太阳辐射数据获取Agent - 只使用NASA POWER API
"""

import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional
from loguru import logger

from core.models import SolarData, LocationInfo
from core.config import settings


class SolarAgent:
    """太阳辐射数据Agent - 只使用NASA POWER API"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'PhotovoltaicAdvisor/1.0'
        })
    
    async def get_solar_data(self, location: LocationInfo, years: int = 1) -> List[SolarData]:
        """
        获取指定位置的太阳辐射数据 - 只使用NASA POWER API
        
        Args:
            location: 位置信息
            years: 获取数据的年数（默认1年）
            
        Returns:
            太阳辐射数据列表
        """
        logger.info(f"开始获取太阳辐射数据: {location.latitude}, {location.longitude}")
        
        # 只使用NASA POWER专业太阳能数据（免费，无需API密钥）
        logger.info("🛰️ 使用NASA POWER获取专业太阳能数据...")
        
        solar_data = await self._get_nasa_power_direct(location)
        if solar_data:
            logger.info(f"✅ NASA POWER API成功获取{len(solar_data)}天专业气象数据")
            return solar_data
        else:
            logger.error("❌ NASA POWER API失败")
            raise Exception("NASA POWER API不可用，请检查网络连接")

    async def _get_nasa_power_direct(self, location: LocationInfo) -> Optional[List[SolarData]]:
        """直接调用NASA POWER API获取太阳辐射数据"""
        try:
            # NASA POWER API URL
            url = "https://power.larc.nasa.gov/api/temporal/daily/point"
            
            # 获取过去一年的数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            
            params = {
                'parameters': 'ALLSKY_SFC_SW_DWN,T2M,WS2M,PRECTOTCORR',  # 太阳辐射、温度、风速、降水
                'community': 'RE',  # 可再生能源
                'longitude': location.longitude,
                'latitude': location.latitude,
                'start': start_date.strftime('%Y%m%d'),
                'end': end_date.strftime('%Y%m%d'),
                'format': 'JSON'
            }
            
            logger.info(f"正在请求NASA POWER API: {location.latitude}, {location.longitude}")
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'properties' in data and 'parameter' in data['properties']:
                    params_data = data['properties']['parameter']
                    irradiance_data = params_data.get('ALLSKY_SFC_SW_DWN', {})
                    temp_data = params_data.get('T2M', {})
                    wind_data = params_data.get('WS2M', {})
                    precip_data = params_data.get('PRECTOTCORR', {})

                    solar_data = []
                    for date_str, irradiance in irradiance_data.items():
                        if irradiance > 0:  # 过滤无效数据
                            temperature = temp_data.get(date_str, 20)
                            wind_speed = wind_data.get(date_str, 2.0)
                            precipitation = precip_data.get(date_str, 0.0)

                            # 创建扩展的SolarData对象
                            solar_data.append(SolarData(
                                date=date_str,
                                irradiance=irradiance,
                                temperature=temperature,
                                wind_speed=wind_speed,
                                precipitation=precipitation
                            ))
                    
                    if solar_data:
                        # 关键修复：按日期排序数据
                        solar_data.sort(key=lambda x: x.date)
                        logger.info(f"NASA POWER API成功获取{len(solar_data)}天数据")
                        logger.info(f"数据时间范围: {solar_data[0].date} 到 {solar_data[-1].date}")
                        
                        # 验证季节性数据
                        self._validate_seasonal_data(solar_data)
                        
                        return solar_data
                    else:
                        logger.error("NASA POWER返回的数据全部无效")
                        return None
                else:
                    logger.error("NASA POWER API响应格式错误")
                    return None
            else:
                logger.error(f"NASA POWER API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"NASA POWER API调用异常: {e}")
            return None
    
    def _validate_seasonal_data(self, solar_data: List[SolarData]) -> None:
        """验证数据的季节性是否正确"""
        monthly_avg = {}
        for data in solar_data:
            month = int(data.date[4:6])
            if month not in monthly_avg:
                monthly_avg[month] = []
            monthly_avg[month].append(data.irradiance)
        
        # 计算月度平均值
        monthly_means = {}
        for month, values in monthly_avg.items():
            if values:
                monthly_means[month] = sum(values) / len(values)
        
        if len(monthly_means) >= 6:  # 至少有6个月的数据
            # 检查夏季是否比冬季高
            summer_months = [6, 7, 8]  # 6-8月
            winter_months = [12, 1, 2]  # 12-2月
            
            summer_avg = sum(monthly_means.get(m, 0) for m in summer_months if m in monthly_means) / len([m for m in summer_months if m in monthly_means])
            winter_avg = sum(monthly_means.get(m, 0) for m in winter_months if m in monthly_means) / len([m for m in winter_months if m in monthly_means])
            
            if summer_avg > winter_avg:
                logger.info(f"✅ 季节性数据验证通过: 夏季{summer_avg:.2f} > 冬季{winter_avg:.2f} kWh/m²/day")
            else:
                logger.warning(f"⚠️ 季节性数据异常: 夏季{summer_avg:.2f} <= 冬季{winter_avg:.2f} kWh/m²/day")
    
    def calculate_monthly_averages(self, solar_data: List[SolarData]) -> List[float]:
        """计算月度平均太阳辐射"""
        if not solar_data:
            return []
        
        # 转换为DataFrame便于处理
        df = pd.DataFrame([{
            'date': datetime.strptime(item.date, '%Y%m%d'),
            'irradiance': item.irradiance
        } for item in solar_data])
        
        # 按月分组计算平均值
        df['month'] = df['date'].dt.month
        monthly_avg = df.groupby('month')['irradiance'].mean()
        
        # 确保12个月都有数据
        result = []
        for month in range(1, 13):
            if month in monthly_avg.index:
                result.append(monthly_avg[month])
            else:
                # 如果某月没有数据，使用相邻月份的平均值
                result.append(self._estimate_missing_month(monthly_avg, month))
        
        return result
    
    def _estimate_missing_month(self, monthly_avg: pd.Series, month: int) -> float:
        """估算缺失月份的数据"""
        available_months = monthly_avg.index.tolist()
        if not available_months:
            return 4.0  # 默认值
        
        # 使用最近月份的数据
        closest_month = min(available_months, key=lambda x: abs(x - month))
        return monthly_avg[closest_month]
    
    def get_annual_average(self, solar_data: List[SolarData]) -> float:
        """计算年平均太阳辐射"""
        if not solar_data:
            return 0.0
        
        total_irradiance = sum(item.irradiance for item in solar_data)
        return total_irradiance / len(solar_data)
    
    def get_peak_sun_hours(self, location: LocationInfo) -> float:
        """
        获取峰值日照小时数（基于纬度的简化估算）
        
        Args:
            location: 位置信息
            
        Returns:
            峰值日照小时数
        """
        # 基于纬度的简化估算
        lat = abs(location.latitude)
        
        if lat < 20:
            return 5.5  # 热带地区
        elif lat < 35:
            return 4.5  # 亚热带地区
        elif lat < 50:
            return 3.5  # 温带地区
        else:
            return 2.5  # 高纬度地区
