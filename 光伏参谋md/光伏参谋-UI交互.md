﻿**光伏参谋-UI交互**

**极简交互 AI Copilot 原型设计文档**

![descript](Aspose.Words.18007d4a-6e76-41ae-8a9d-806f7f4eb968.001.png)
## **1. 设计原则**
- **极简输入**：全程通过一个文本输入框，支持自然语言与简洁命令。
- **即时反馈**：结果以小组件卡片形式在对话窗口呈现。
- **无多余导航**：合并所有功能于单个聊天页面，无侧栏、弹窗。
- **轻量展示**：图表和数值在聊天消息中内嵌展示，支持展开/收起。

  ## **2. 主界面布局**
  仅保留一个主聊天窗口，包含：



























  ![](Aspose.Words.18007d4a-6e76-41ae-8a9d-806f7f4eb968.002.png)

- 文本气泡样式：类似即时通讯，无侧边栏。
- 卡片简洁：关键信息、一行一条。

  ## **3. 交互流程**
1. **唤起**：用户打开聊天页，AI 打招呼。
1. **输入地点**：用户输入自然语言地址或经纬度。
1. **后台计算**：AI 在后台串行地完成位置解析 → 光照数据拉取 → 发电模拟。
1. **结果展示**：AI 发送“结果卡片”消息，并可点击“展开详细”。
1. **深度查看**：点击“展开详细”后，AI 追加发送月度折线图和数据表格摘要。
1. **导出/分享**：用户发送“导出”或“分享”命令，AI 返回下载链接或分享短链。

   ## **4. 聊天消息示例**
   **用户**：北京天安门

   **AI**：

   📍 已解析：39.9087,116.3975 ⏳ 评估中...

   **AI**：

   📈 \*\*年发电量：\*\*12,000 kWh

   🔋 \*\*月均发电：\*\*1,000 kWh

   🎯 \*\*建议倾角：\*\*28°（+6%增益）

   [展开详细](https://chatgpt.com/c/6878cb9a-b9e0-800e-aee7-3f37823a561c#)

   **用户**：展开详细

   **AI**：

   📊 月度发电折线图

   ||1月|2月|...|12月|| ||800|850|...|1100||

   **用户**：导出报告

   **AI**：已生成 PDF，点击下载： [下载链接]

   ## **5. 组件与技术实现**
- **前端**：Chainlit 或 React+Chat UI 库，单页应用。
- **智能体框架**：LangGraph/CrewAI 驱动串行节点。
- **可视化**：使用 ECharts 或 Chart.js 在消息中内嵌渲染。
- **数据拉取**：NASA POWER API 后台缓存。
- **导出**：FastAPI 提供临时 PDF 下载链接。

  **设计要点**：

- 全程聊天交互，最大程度减少点击与页面跳转。
- 一次对话即可完成评估、查看、导出。
- 使用动态消息加载和卡片组件营造流畅体验。

  ***End***

