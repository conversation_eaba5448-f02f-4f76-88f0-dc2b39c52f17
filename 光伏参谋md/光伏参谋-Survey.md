﻿**光伏参谋-Survey**
# **获取光照数据**
获取指定经纬度的**历史光照数据（太阳辐射/日照时数/总太阳辐射）**，可以通过以下几种方式：

## **✅ 一、权威公共数据源**
### **1. NASA POWER Project**
- 📍[NASA POWER | Data Access Viewer (DAV)](https://power.larc.nasa.gov/data-access-viewer/)
- **数据内容**：全球太阳辐射、日照、气温、风速等
- **历史范围**：1981年至今（有的可到1960年）
- **时间粒度**：每日、每小时
- **API 文档**：[NASA POWER | Docs | Data Services | API Overview - NASA POWER | Docs](https://power.larc.nasa.gov/docs/services/api/)

  **示例 API 请求：**



  ![ref1]

- ALLSKY\_SFC\_SW\_DWN：全天空地表短波向下辐射（单位：kWh/m²/day）

  ### **2. Meteostat**
- 🌐 [The Weather's Record Keeper | Meteostat](https://meteostat.net/en/)
- **数据内容**：太阳辐射、云量、天气等
- **接口**：[Meteostat Developers](https://dev.meteostat.net/)
- **历史范围**：从1970年代开始，具体看站点
- **优点**：可获取地面观测站数据

  **使用示例：**













  ![](Aspose.Words.6bb61543-289c-40ef-9887-90f231c776d3.002.png)

  ### **3. Copernicus (欧洲中期天气预报中心 ECMWF)**
- 🌐 [Climate Data Store](https://cds.climate.copernicus.eu/)[Climate Data Store](https://cds.climate.copernicus.eu)
- 数据：ERA5 reanalysis 提供全球历史太阳辐射数据（hourly）
- 支持下载 NetCDF 格式数据，可精细到小时粒度

  ## **✅ 二、国内数据渠道（如中国气象局）**
  ### **4. 中国气象局气象数据中心**
- 🌐 [国家气象信息中心-中国气象数据网](http://data.cma.cn/)
- **数据类型**：日照时数、太阳总辐射、紫外线辐射
- **限制**：部分数据需注册、审核、付费
- 可按站点（如国家气象站）获取历史数据

  ## **✅ 三、商业/开源光照模拟工具**
  ### **5. PVGIS - Photovoltaic Geographical Information System**
- 🌐 [JRC Photovoltaic Geographical Information System (PVGIS) - European Commission](https://re.jrc.ec.europa.eu/pvg_tools/en/)
- 由欧盟提供，专注太阳能应用
- 输入经纬度后可获得每日/每小时辐照量
- 也提供 **API**

  ## **✅ 四、工具推荐**

  |**工具/平台**|**数据粒度**|**历史范围**|**是否免费**|**是否有API**|
  | :-: | :-: | :-: | :-: | :-: |
  |NASA POWER|日/小时|1981~今|✅免费|✅有|
  |Meteostat|日/小时|1970~今|✅免费|✅有|
  |ERA5 (Copernicus)|小时级|1950~今|✅免费|✅有|
  |CMA中国气象局|日|数十年|部分免费|🚫（下载为主）|
  |PVGIS|日/小时|长期|✅免费|✅有|

  ## **✅ 结论推荐**
  如果你想获取**指定经纬度的历史光照数据**，建议如下：

- \*简单快速获取：\*\*用 **NASA POWER API**（适合全球）
- \*精确地面站点数据：\*\*用 **Meteostat**
- \*研究级精度：\*\*用 **ERA5 + CDS API**
- \*国内权威数据：\*\*中国气象局站点数据

  # **获取卫星数据**
  获取**最新的卫星地图数据**（尤其是高清、接近实时的图像），通常可通过以下几种方式：

  ## **✅ 一、免费开源数据源（适合科研、开发）**
  ### **1. Sentinel Hub（欧盟 Copernicus）**
- 🌍 [Sentinel Hub](https://www.sentinel-hub.com/)
- 提供：Sentinel-2（每 5 天覆盖全球一次，10m 分辨率）
- 实时性：3~5天以内的更新
- 分辨率：10m、20m、60m
- **免费额度**：EO Browser 可在线查看，API 有免费额度

  **可视化平台**：[Sentinel Hub EO Browser](https://apps.sentinel-hub.com/eo-browser)

- \*API使用方式：\*\*可调用 Sentinel Hub API 获取 GeoTIFF、PNG、JPG、NDVI 等图层

  ### **2. NASA Worldview**
- 🌍 [NASA Worldview](https://worldview.earthdata.nasa.gov/)
- 数据源：MODIS、VIIRS 等
- 更新频率：每日甚至3小时
- 分辨率：250m ~ 1km（适合大尺度分析）
- 可下载原始图像（GeoTIFF 等格式）
- 优点：**支持全球快速预览+图层叠加**

  ### **3. Google Earth Engine**
- 🌍 <https://earthengine.google.com/>
- 接入 Sentinel、Landsat、MODIS 等多个卫星源
- 可写 JavaScript 或 Python 脚本，进行区域数据提取、处理、分析
- 注册后可访问历史及最新数据

  **示例代码：**















  ![](Aspose.Words.6bb61543-289c-40ef-9887-90f231c776d3.003.png)

  ## **✅ 二、商用高分辨率卫星数据（近实时、精准）**

  |**平台**|**分辨率**|**更新频率**|**获取方式**|**免费试用**|
  | :-: | :-: | :-: | :-: | :-: |
  |**Maxar (原 DigitalGlobe)**|最高 30cm|天级|商业授权|❌|
  |**Planet Labs**|3-5m|每日|商业订阅，教育免费|✅教育可用|
  |**Airbus Pléiades**|50cm|天级|商业|❌|

  👉 如果你需要**高清城市细节图（<1米）**，通常需要：

- **购买商业卫星图**（如 Maxar、Airbus）
- 或使用百度地图、腾讯地图、高德地图 API（这些使用了商业卫星底图）

  ## **✅ 三、地图服务 API（可嵌入 Web 或移动应用）**
  ### **1. Google Maps Static API / Tiles API**
- 免费层不提供原始卫星影像数据下载（仅做展示）
- 无法确定时间戳（不是“最新”图像）
- 商用需付费授权
- 卫星图多为拼接后的历史影像
  ### **2. Bing Maps / Esri / Mapbox Satellite**
- 同 Google Maps 类似，不适合做时间敏感分析
- Mapbox Satellite 可提供 JSON 图块服务

  ## **✅ 四、特定需求推荐**

  |**需求**|**推荐平台**|
  | :-: | :-: |
  |获取最新（<5天）的城市卫星图|Sentinel-2 + Sentinel Hub|
  |获取小时级天气云图|NASA Worldview（VIIRS、MODIS）|
  |获取高分辨率图像（<1米）|Maxar、Airbus、Planet|
  |国内城市高清地图|百度地图、高德地图卫星图（但无法导出原图）|
  |地理 AI 模型训练数据|Google Earth Engine 或 Radiant ML Hub|

  ## **✅ 结论**
  ### **如果你是开发者/研究者，推荐组合使用：**
- **Sentinel Hub**（获取最新、免费、高分辨率图）
- **Google Earth Engine**（历史+多源+脚本化）
- **NASA Worldview**（快速全球查看）

  # **简单方案**
  你可以构建一个基于**历史光照数据 + 地理参数**的光伏发电预估系统，预测**指定经纬度地点的光伏装机潜力**（预期发电量）。这套系统可以精确到天、月、年级别，甚至小时级模拟。

  ## **✅ 一、基本原理**
  ### **光伏发电量估算公式（简化版）：**
  E=H×A×η×PRE = H \times A \times \eta \times PR

  其中：

- E：单位时间内发电量（kWh）
- H：光照强度（kWh/m²/day 或 month）——**历史光照数据**
- A：光伏面板面积（m²）——用户自定义或按装机容量反推
- η：光伏组件效率（通常 15%~22%）
- PR：系统性能比（Performance Ratio，一般 0.75~0.85，考虑损耗、温度、阴影、逆变器效率等）

  ## **✅ 二、完整技术实现流程**
  ### **✅ Step 1：获取历史光照数据（H）**
  ### **推荐方式：**
- 使用 **NASA POWER API** 获取 ALLSKY\_SFC\_SW\_DWN（单位：kWh/m²/day）
- 或 Sentinel/ERA5 光照数据（更细粒度）

  **示例 API 请求（2022年上海全年光照）：**



  ![ref1]

  返回的 ALLSKY\_SFC\_SW\_DWN 是每平方米每日接收到的太阳能量。

  ### **✅ Step 2：配置光伏系统参数（A、η、PR）**
  你需要设定或采集以下参数：

- 面板安装面积 A，例如 20 平方米
- 面板转换效率 η，例如 0.18
- 性能比 PR，例如 0.80
- 朝向（南/东南/西南）
- 倾角（当地纬度 ±10 度较优）

  可选：加入地形遮挡分析（例如通过 DEM 数据分析阴影）

  ### **✅ Step 3：计算每日或月度发电量**
  ### **示例（Python 伪代码）：**



























  ![](Aspose.Words.6bb61543-289c-40ef-9887-90f231c776d3.004.png)

  ## **✅ 三、进一步增强（高级功能）**
  ### **✅ 地形+倾角修正（可选）**
- 使用 [pvlib python &#8212; pvlib python 0.13.0 documentation](https://pvlib-python.readthedocs.io/en/stable/) 进行精确模拟
- 考虑安装角度、方向、温度、阴影
  ### **✅ 系统模拟工具推荐：**
- pvlib（Python）
- SAM（NREL的 System Advisor Model）
- PVsyst（商业级模拟工具）
- reopt（NREL在线建模）
- PVGIS API（欧洲版模拟接口）

  ## **✅ 四、使用 PVLib 实现（真实场景模拟）**



































  ![](Aspose.Words.6bb61543-289c-40ef-9887-90f231c776d3.005.png)

  ## **✅ 五、最终目标：创建“光伏潜力分析引擎”**
  你可以将上面这些能力，构建为一个小型的服务或产品模块，支持输入经纬度 → 自动分析：

- 年度发电预估（kWh）
- 每月分布图（发电热力图）
- 是否适合屋顶光伏（yes/no）
- 建议倾角与朝向

  ## **✅ 总结**

  |**步骤**|**工具/数据源**|
  | :-: | :-: |
  |获取历史光照|NASA POWER、ERA5、Sentinel-2|
  |光伏建模计算|PVLib（推荐）、SAM、PVGIS|
  |API构建/批量分析|Python + FastAPI / Streamlit|
  |地理信息支持|DEM 地形数据 + GeoTIFF + rasterio|

  如果你告诉我你的需求场景：

- 是否做成 Web 应用 / API 服务
- 输入数量（单点 vs 大批量）
- 期望结果格式（图表？Excel？地图？）

  我可以为你**定制架构设计或代码模板**，是否需要？
  # **智能体**
  非常好！将“光伏发电潜力评估”功能通过 **AI智能体** 实现，是非常实用、且具扩展潜力的场景。这个智能体可以帮助非技术用户输入一个地理位置，即可获得“是否适合装光伏”“年发电预期”等结构化、可视化信息。

  ## **✅ 一、智能体能力定义（AI Copilot）**
  这个智能体可具备以下 **核心能力（技能）**：

  |**能力**|**说明**|
  | :-: | :-: |
  |📍 地点解析|用户输入地址/地名/经纬度 → 解析经纬度|
  |☀️ 历史光照获取|自动从 NASA POWER / PVGIS 等拉取光照数据|
  |🔧 光伏系统模拟|模拟固定参数系统（或允许用户设定容量、倾角）|
  |⚡ 发电预估|计算年、月级别发电量|
  |📊 可视化|生成图表、地图、表格结果|
  |🧠 自我解释|解释模拟逻辑、结果依据、推荐设置|
  |🗂️ 报告生成|一键生成 PDF / HTML 报告|

  ## **✅ 二、推荐技术架构（以 LangGraph 为例）**
  你可以用如下结构实现智能体：























  ![](Aspose.Words.6bb61543-289c-40ef-9887-90f231c776d3.006.png)

  可以用 LangGraph 或 CrewAI 创建这样的多节点智能体图谱，前端通过 Chainlit / Streamlit / ChatUI 驱动。

  ## **✅ 三、模块拆解建议**
  ### **🧠 1. 地理解析 Agent**
- 输入：自然语言地址、地名、或经纬度
- 工具：Google Maps Geocoding API / OpenStreetMap Nominatim
- 输出：标准纬度、经度、国家、省市等

  ### **☀️ 2. 光照数据拉取 Agent**
- 输入：经纬度、起止时间
- 工具： NASA POWER API
  - PVGIS API
  - Meteostat（可选）
- 输出：每日光照数据表（kWh/m²/day）

  ### **⚙️ 3. 光伏模拟 Agent**
- 输入：光照数据 + 用户参数（面积/倾角/方向/组件效率）
- 工具： 使用 Python + pvlib 进行真实模拟
  - 简化版：使用经验模型（如 E = H × A × η × PR）

    ### **📊 4. 可视化 & 报告生成 Agent**
- 输出： 月度/年度发电曲线图
  - 日照地图（可选）
  - HTML / Markdown 报告
- 工具： matplotlib / plotly / seaborn
  - Jinja2 + WeasyPrint（生成PDF）

    ### **🧠 5. Copilot交互引导（Prompt工程）**
    你可以设计提示词引导用户对话：







    ![](Aspose.Words.6bb61543-289c-40ef-9887-90f231c776d3.007.png)

    ## **✅ 四、前端实现建议**

    |**类型**|**推荐框架**|**说明**|
    | :-: | :-: | :-: |
    |Web Copilot|Chainlit、Streamlit、Gradio|与 LangGraph 配合最优|
    |地图输入|leaflet.js、mapbox-gl-js|地图点击选点，反查经纬度|
    |移动端|React Native + MapKit/WebView|可做成 App|

    ## **✅ 五、AI 智能体执行图谱设计（LangGraph）**
    graph TD   A[用户输入地址/经纬度]   B[解析地理位置]   C[获取光照数据]   D[运行光伏模型]   E[生成图表]   F[输出报告 & 可视化]   A --> B --> C --> D --> E --> F  
    ## **✅ 六、增值扩展能力（可选）**

    |**能力**|**描述**|
    | :-: | :-: |
    |☁️ 天气预报集成|结合未来天气预测未来一周发电情况|
    |🏠 屋顶识别（遥感）|调用卫星图 + CV 模型判断屋顶大小|
    |📍 区域批量分析|批量分析一个城市/区域所有屋顶光伏潜力|
    |💸 投资回报分析|加入成本、电价、补贴 → ROI估算|
    |🧠 多模态接入|上传照片或地图 → AI识别位置和方向|

    ## **✅ 七、推荐工具生态**

    |**功能**|**推荐工具**|
    | :-: | :-: |
    |智能体框架|LangGraph + Chainlit / CrewAI|
    |地图输入|Leaflet、Mapbox、CesiumJS|
    |API 数据源|NASA POWER、PVGIS、OpenStreetMap|
    |PV 模拟|pvlib（Python库）|
    |可视化|matplotlib、plotly、seaborn|
    |报告生成|Jinja2 + HTML + WeasyPrint|

    ## **✅ 示例应用场景**
- 🌇 个人或企业查询“我家屋顶一年能发多少电？”
- 🏙️ 政府或能源公司批量分析城市光伏潜力
- 🛰️ 遥感数据 + AI 模型推荐最适合装光伏的地点
- 🛠️ 光伏安装公司给客户做可视化方案展示

  ## **✅ 总结：你的智能体可以长这样：**
  用户：我家在广州越秀区，屋顶大概50平，能装光伏吗？每年能发多少电？

  **AI Copilot**：已为你获取越秀区历史光照数据，模拟一个倾角30度、南向的20kW光伏系统，预计年发电量为23,500 kWh，约可节省电费 ¥18,800 元。需要我为你生成可视化报告吗？

  如果你希望我：

- 帮你生成一个完整的 LangGraph 智能体项目结构
- 编写 pvlib 模拟模块
- 创建一个 Chainlit + 地图上传界面

  告诉我你更倾向于 **API 服务 / Web 应用 / 聊天助手**，我可以一步步帮你搭建原型。需要吗？

[ref1]: Aspose.Words.6bb61543-289c-40ef-9887-90f231c776d3.001.png
