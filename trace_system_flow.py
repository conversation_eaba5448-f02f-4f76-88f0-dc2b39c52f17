#!/usr/bin/env python3
"""
生成光伏参谋系统代码级别的详细流程图
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams
from loguru import logger

async def trace_system_flow():
    """跟踪系统完整流程，展示参数来源"""
    
    logger.info("🔍 开始跟踪光伏参谋系统完整流程...")
    
    # ===== 1. 用户输入阶段 =====
    user_input = "北京市 5kW"
    logger.info(f"\n📥 [用户输入] {user_input}")
    
    # ===== 2. 地理位置解析阶段 =====
    logger.info("\n🗺️ [第一阶段] 地理位置解析")
    geo_agent = GeoAgent()
    
    logger.info("├─ 调用: GeoAgent.parse_location()")
    logger.info("├─ 参数来源: 用户输入地址")
    
    location_info = await geo_agent.parse_location("北京市")
    
    if location_info:
        logger.info("├─ 高德地图API响应:")
        logger.info(f"│  ├─ 纬度: {location_info.latitude} (来源: 高德地图地理编码)")
        logger.info(f"│  ├─ 经度: {location_info.longitude} (来源: 高德地图地理编码)")
        logger.info(f"│  ├─ 城市: {location_info.city} (来源: 高德地图)")
        logger.info(f"│  └─ 省份: {location_info.province} (来源: 高德地图)")
        
        logger.info("├─ 海拔数据获取:")
        logger.info("│  ├─ 调用: _get_altitude()")
        logger.info("│  ├─ 尝试: Open Elevation API")
        if location_info.altitude:
            logger.info(f"│  └─ 海拔: {location_info.altitude}米 (来源: Open Elevation API)")
        
        logger.info("└─ LocationInfo对象创建完成")
    
    # ===== 3. 太阳辐射数据获取阶段 =====
    logger.info("\n☀️ [第二阶段] 太阳辐射数据获取")
    solar_agent = SolarAgent()
    
    logger.info("├─ 调用: SolarAgent.get_solar_data()")
    logger.info("├─ 输入参数: LocationInfo对象")
    logger.info("├─ NASA POWER API调用:")
    logger.info("│  ├─ 参数: ALLSKY_SFC_SW_DWN (太阳辐射)")
    logger.info("│  ├─ 参数: T2M (2米高度温度)")
    logger.info("│  ├─ 参数: RH2M (相对湿度)")
    logger.info("│  └─ 参数: WS10M (10米风速)")
    
    solar_data = await solar_agent.get_solar_data(location_info, years=1)
    
    if solar_data and len(solar_data) > 0:
        avg_irradiance = sum(data.irradiance for data in solar_data) / len(solar_data)
        avg_temperature = sum(data.temperature for data in solar_data) / len(solar_data)
        
        logger.info("├─ 数据处理流程:")
        logger.info("│  ├─ 调用: _generate_data_from_nasa_power()")
        logger.info("│  ├─ 海拔修正:")
        logger.info("│  │  ├─ 调用: _calculate_altitude_factor()")
        logger.info(f"│  │  ├─ 输入: altitude={location_info.altitude}米")
        
        altitude_factor = solar_agent._calculate_altitude_factor(location_info.altitude)
        logger.info(f"│  │  └─ 输出: altitude_factor={altitude_factor:.3f}")
        
        logger.info("│  ├─ 温度修正:")
        logger.info("│  │  ├─ 调用: _correct_t2m_temperature()")
        logger.info("│  │  ├─ 输入: NASA T2M温度数据")
        logger.info(f"│  │  ├─ 海拔修正: -{location_info.altitude/1000*2:.1f}°C")
        logger.info(f"│  │  └─ 修正后平均温度: {avg_temperature:.1f}°C")
        
        logger.info("│  └─ 太阳辐射修正:")
        logger.info("│     ├─ 基础辐射: NASA卫星数据")
        logger.info("│     ├─ 大气透过率: 海拔因子修正")
        logger.info(f"│     └─ 修正后平均辐射: {avg_irradiance:.3f} kWh/m²/day")
        
        logger.info(f"└─ SolarData对象数组: {len(solar_data)}天数据")
    
    # ===== 4. 光伏系统参数设置 =====
    logger.info("\n⚡ [第三阶段] 光伏系统参数设置")
    
    # 从用户输入提取系统容量
    capacity = 5.0  # 从"5kW"提取
    
    logger.info("├─ 系统参数来源:")
    logger.info(f"│  ├─ 装机容量: {capacity}kW (来源: 用户输入)")
    logger.info("│  ├─ 组件效率: 20% (来源: 系统默认)")
    logger.info("│  ├─ 系统效率: 85% (来源: 系统默认)")
    logger.info("│  └─ 性能比: 80% (来源: 系统默认)")
    
    # 计算最优倾角
    from core.utils import calculate_optimal_tilt
    optimal_tilt = calculate_optimal_tilt(location_info.latitude)
    
    logger.info("├─ 系统优化:")
    logger.info("│  ├─ 调用: calculate_optimal_tilt()")
    logger.info(f"│  ├─ 输入: latitude={location_info.latitude}")
    logger.info(f"│  ├─ 最优倾角: {optimal_tilt:.1f}° (基于纬度计算)")
    logger.info("│  └─ 最优方位角: 180° (正南朝向)")
    
    pv_params = PVSystemParams(
        capacity_kw=capacity,
        tilt_angle=optimal_tilt,
        azimuth=180.0,
        module_efficiency=0.20,
        system_efficiency=0.85,
        performance_ratio=0.80
    )
    
    logger.info("└─ PVSystemParams对象创建完成")
    
    # ===== 5. 发电量计算阶段 =====
    logger.info("\n🔋 [第四阶段] 发电量计算")
    pv_agent = PVAgent()
    
    logger.info("├─ 调用: PVAgent.calculate_power_generation()")
    logger.info("├─ 输入参数:")
    logger.info("│  ├─ SolarData: 太阳辐射和温度数据")
    logger.info("│  └─ PVSystemParams: 系统参数")
    
    power_generation = await pv_agent.calculate_power_generation(location_info, solar_data, pv_params)
    
    if power_generation:
        logger.info("├─ 计算流程:")
        logger.info("│  ├─ 基础发电量计算:")
        logger.info("│  │  ├─ 公式: 辐射量 × 装机容量 × 系统效率")
        logger.info(f"│  │  ├─ 平均辐射: {avg_irradiance:.3f} kWh/m²/day")
        logger.info(f"│  │  ├─ 装机容量: {capacity} kW")
        logger.info(f"│  │  └─ 系统效率: {pv_params.system_efficiency}")
        
        logger.info("│  ├─ 温度修正:")
        logger.info("│  │  ├─ 温度系数: -0.004%/°C")
        logger.info(f"│  │  ├─ 平均温度: {avg_temperature:.1f}°C")
        logger.info("│  │  └─ 修正公式: 基础发电量 × (1 + 温度系数 × (温度-25))")
        
        logger.info("│  └─ 系统优化:")
        logger.info(f"│     ├─ 倾角优化: {optimal_tilt:.1f}° (基于纬度{location_info.latitude:.1f}°)")
        logger.info("│     └─ 方位角: 180° (正南朝向)")
        
        logger.info("├─ 计算结果:")
        logger.info(f"│  ├─ 年发电量: {power_generation.annual_generation:.0f} kWh")
        logger.info(f"│  ├─ 月均发电量: {power_generation.monthly_average:.0f} kWh")
        logger.info(f"│  └─ 发电量数据: {len(power_generation.daily_generation)}天")
        
        logger.info("└─ PowerGeneration对象创建完成")
    
    # ===== 6. 参数来源总结 =====
    logger.info("\n📊 [参数来源总结]")
    logger.info("├─ 地理数据:")
    logger.info("│  ├─ 坐标: 高德地图API地理编码")
    logger.info("│  └─ 海拔: Open Elevation API + DEM数据")
    logger.info("├─ 气象数据:")
    logger.info("│  ├─ 太阳辐射: NASA POWER API卫星观测")
    logger.info("│  ├─ 温度: NASA T2M + 海拔修正算法")
    logger.info("│  └─ 大气透过率: 海拔因子 + 物理模型")
    logger.info("├─ 系统参数:")
    logger.info("│  ├─ 装机容量: 用户输入提取")
    logger.info("│  ├─ 最优倾角: 纬度计算算法")
    logger.info("│  └─ 系统效率: 行业标准默认值")
    logger.info("└─ 计算模型:")
    logger.info("   ├─ 发电量: PVWatts国际标准模型")
    logger.info("   ├─ 温度修正: -0.004%/°C系数")
    logger.info("   └─ 海拔修正: 大气物理学原理")
    
    logger.info("\n🎯 流程跟踪完成！")
    
    return {
        'location_info': location_info,
        'solar_data_count': len(solar_data) if solar_data else 0,
        'avg_irradiance': avg_irradiance if solar_data else 0,
        'avg_temperature': avg_temperature if solar_data else 0,
        'altitude_factor': altitude_factor if location_info else 0,
        'annual_generation': power_generation.annual_generation if power_generation else 0,
        'optimal_tilt': optimal_tilt if location_info else 0
    }

if __name__ == "__main__":
    result = asyncio.run(trace_system_flow())
    print(f"\n📈 最终结果摘要: {result}")
