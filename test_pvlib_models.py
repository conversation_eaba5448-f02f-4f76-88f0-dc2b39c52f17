#!/usr/bin/env python3
"""
测试不同的PVLib模型
"""

import pandas as pd
import pvlib
import numpy as np
from datetime import datetime

def test_different_models():
    """测试SAPM、CEC和PVWatts模型"""
    print("🧪 测试不同的PVLib模型...")
    
    # 创建测试数据
    times = pd.date_range('2024-06-21 06:00', '2024-06-21 18:00', freq='h', tz='Asia/Shanghai')
    
    # 夏至日的理想辐射数据
    ghi_values = []
    for hour in range(len(times)):
        if 6 <= hour <= 12:
            ghi = 200 + (hour - 6) * 150  # 200-1100 W/m²
        elif 12 < hour <= 18:
            ghi = 1100 - (hour - 12) * 150  # 1100-200 W/m²
        else:
            ghi = 0
        ghi_values.append(ghi)
    
    weather_data = pd.DataFrame({
        'ghi': ghi_values,
        'dhi': [g * 0.3 for g in ghi_values],
        'dni': [g * 0.7 / 0.8 for g in ghi_values],
        'temp_air': [25] * len(times),
        'wind_speed': [2.0] * len(times),
        'albedo': [0.2] * len(times),
        'precipitable_water': [20.0] * len(times)
    }, index=times)
    
    # 北京位置
    location = pvlib.location.Location(latitude=40.1, longitude=116.3, tz='Asia/Shanghai')
    
    # 获取数据库
    print("📚 加载PVLib数据库...")
    try:
        sandia_modules = pvlib.pvsystem.retrieve_sam('SandiaMod')
        cec_modules = pvlib.pvsystem.retrieve_sam('CECMod')
        cec_inverters = pvlib.pvsystem.retrieve_sam('CECInverter')
        
        print(f"  Sandia组件数据库: {len(sandia_modules)}个组件")
        print(f"  CEC组件数据库: {len(cec_modules)}个组件")
        print(f"  CEC逆变器数据库: {len(cec_inverters)}个逆变器")
        
        # 选择合适的组件和逆变器
        # 选择一个接近10kW的系统配置
        
        # 查找合适的组件 (约400-500W)
        suitable_modules = []
        for name, params in cec_modules.items():
            if 400 <= params.get('STC', 0) <= 500:  # STC功率在400-500W
                suitable_modules.append((name, params))
        
        if suitable_modules:
            module_name, cec_module = suitable_modules[0]
            print(f"  选择CEC组件: {module_name} ({cec_module['STC']:.0f}W)")
        else:
            # 备选：使用Sandia数据库
            module_name = 'Canadian_Solar_CS5P_220M___2009_'
            cec_module = None
            print(f"  选择Sandia组件: {module_name}")

        # 总是获取Sandia组件用于SAPM测试
        sandia_module_name = 'Canadian_Solar_CS5P_220M___2009_'
        sandia_module = sandia_modules[sandia_module_name]
        
        # 查找合适的逆变器 (约10kW)
        suitable_inverters = []
        for name, params in cec_inverters.items():
            pac_max = params.get('Paco', 0)
            if 9000 <= pac_max <= 12000:  # AC功率在9-12kW
                suitable_inverters.append((name, params))
        
        if suitable_inverters:
            inverter_name, cec_inverter = suitable_inverters[0]
            print(f"  选择CEC逆变器: {inverter_name} ({cec_inverter['Paco']:.0f}W)")
        else:
            print("  未找到合适的CEC逆变器")
            return
            
    except Exception as e:
        print(f"❌ 数据库加载失败: {e}")
        return
    
    # 温度模型参数
    temperature_model_parameters = pvlib.temperature.TEMPERATURE_MODEL_PARAMETERS['sapm']['open_rack_glass_glass']
    
    # 测试不同模型
    models_to_test = []
    
    # 1. CEC模型 (如果有合适的组件)
    if suitable_modules:
        # 计算需要的组件数量来达到约10kW
        module_power = cec_module['STC']
        modules_needed = int(10000 / module_power)
        
        cec_system = pvlib.pvsystem.PVSystem(
            surface_tilt=30,
            surface_azimuth=180,
            module_parameters=cec_module,
            inverter_parameters=cec_inverter,
            temperature_model_parameters=temperature_model_parameters,
            modules_per_string=modules_needed,
            strings_per_inverter=1
        )
        
        models_to_test.append({
            'name': f'CEC模型 ({modules_needed}个{module_power:.0f}W组件)',
            'system': cec_system,
            'dc_model': 'cec',
            'ac_model': 'sandia'
        })
    
    # 2. SAPM模型
    sapm_system = pvlib.pvsystem.PVSystem(
        surface_tilt=30,
        surface_azimuth=180,
        module_parameters=sandia_module,
        inverter_parameters=cec_inverter,
        temperature_model_parameters=temperature_model_parameters,
        modules_per_string=45,  # 45个220W组件 ≈ 10kW
        strings_per_inverter=1
    )
    
    models_to_test.append({
        'name': 'SAMP模型 (45个220W组件)',
        'system': sapm_system,
        'dc_model': 'sapm',
        'ac_model': 'sandia'
    })
    
    # 3. PVWatts模型 (作为对比)
    pvwatts_system = pvlib.pvsystem.PVSystem(
        surface_tilt=30,
        surface_azimuth=180,
        module_parameters={'pdc0': 10000, 'gamma_pdc': -0.004},
        inverter_parameters={'pdc0': 10000, 'eta_inv_nom': 0.96},
        temperature_model_parameters=temperature_model_parameters
    )
    
    models_to_test.append({
        'name': 'PVWatts模型 (10kW)',
        'system': pvwatts_system,
        'dc_model': 'pvwatts',
        'ac_model': 'pvwatts'
    })
    
    # 运行测试
    for model_config in models_to_test:
        print(f"\n📊 测试 {model_config['name']}")
        
        try:
            mc = pvlib.modelchain.ModelChain(
                model_config['system'], 
                location,
                dc_model=model_config['dc_model'],
                ac_model=model_config['ac_model'],
                aoi_model='physical',
                spectral_model='no_loss',
                temperature_model='sapm'
            )
            
            # 运行模型
            mc.run_model(weather_data)
            
            # 分析结果
            dc_power = mc.results.dc
            ac_power = mc.results.ac
            
            if isinstance(dc_power, tuple):
                # 多个数组的情况，取第一个
                dc_power = dc_power[0]
                if hasattr(dc_power, 'p_mp'):
                    dc_power = dc_power['p_mp']
            elif hasattr(dc_power, 'p_mp'):
                dc_power = dc_power['p_mp']
            
            if isinstance(ac_power, tuple):
                ac_power = ac_power[0]
            
            daily_dc_energy = dc_power.sum() / 1000  # kWh
            daily_ac_energy = ac_power.sum() / 1000  # kWh
            peak_dc_power = dc_power.max()
            peak_ac_power = ac_power.max()
            
            print(f"  峰值DC功率: {peak_dc_power:.0f}W")
            print(f"  峰值AC功率: {peak_ac_power:.0f}W")
            print(f"  日DC发电量: {daily_dc_energy:.2f} kWh")
            print(f"  日AC发电量: {daily_ac_energy:.2f} kWh")
            print(f"  系统效率: {daily_ac_energy/daily_dc_energy:.3f}")
            
            # 年发电量估算
            annual_estimate = daily_ac_energy * 365
            print(f"  年发电量估算: {annual_estimate:.0f} kWh")
            
        except Exception as e:
            print(f"  ❌ 模型运行失败: {e}")

if __name__ == "__main__":
    test_different_models()
