#!/usr/bin/env python3
"""
测试PVWatts模型的参数理解
"""

import pandas as pd
import pvlib
import numpy as np
from datetime import datetime

def test_pvwatts_parameters():
    """测试不同PVWatts参数配置"""
    print("🧪 测试PVWatts模型参数理解...")
    
    # 创建简单的测试数据
    times = pd.date_range('2024-06-21 06:00', '2024-06-21 18:00', freq='h', tz='Asia/Shanghai')
    
    # 夏至日的理想辐射数据
    ghi_values = []
    for hour in range(len(times)):
        if 6 <= hour <= 12:
            # 上午递增
            ghi = 200 + (hour - 6) * 150  # 200-1100 W/m²
        elif 12 < hour <= 18:
            # 下午递减
            ghi = 1100 - (hour - 12) * 150  # 1100-200 W/m²
        else:
            ghi = 0
        ghi_values.append(ghi)
    
    weather_data = pd.DataFrame({
        'ghi': ghi_values,
        'dhi': [g * 0.3 for g in ghi_values],  # 30%散射
        'dni': [g * 0.7 / 0.8 for g in ghi_values],  # 简化DNI
        'temp_air': [25] * len(times),
        'wind_speed': [2.0] * len(times),
        'albedo': [0.2] * len(times),
        'precipitable_water': [20.0] * len(times)
    }, index=times)
    
    # 北京位置
    location = pvlib.location.Location(latitude=40.1, longitude=116.3, tz='Asia/Shanghai')
    
    # 测试不同的参数配置
    test_configs = [
        {
            'name': '配置1: pdc0=10000W (理解为系统总容量)',
            'pdc0': 10000,
            'modules_per_string': 1,
            'strings': 1
        },
        {
            'name': '配置2: pdc0=500W, 20个组件 (理解为单组件功率)',
            'pdc0': 500,
            'modules_per_string': 20,
            'strings': 1
        },
        {
            'name': '配置3: pdc0=250W, 20串2并 (理解为单组件功率)',
            'pdc0': 250,
            'modules_per_string': 20,
            'strings': 2
        }
    ]
    
    for config in test_configs:
        print(f"\n📊 {config['name']}")
        
        # 创建系统
        module_parameters = {
            'pdc0': config['pdc0'],
            'gamma_pdc': -0.004,
            'temp_ref': 25.0
        }
        
        inverter_parameters = {
            'pdc0': 10000,  # 10kW逆变器
            'eta_inv_nom': 0.96,
            'eta_inv_ref': 0.9635
        }
        
        temperature_model_parameters = {
            'a': -3.47,
            'b': -0.0594,
            'deltaT': 3.0
        }
        
        system = pvlib.pvsystem.PVSystem(
            surface_tilt=30,
            surface_azimuth=180,
            module_parameters=module_parameters,
            inverter_parameters=inverter_parameters,
            temperature_model_parameters=temperature_model_parameters,
            modules_per_string=config['modules_per_string'],
            strings_per_inverter=config['strings'],
            racking_model='open_rack'
        )
        
        mc = pvlib.modelchain.ModelChain(
            system, location,
            dc_model='pvwatts',
            ac_model='pvwatts',
            aoi_model='no_loss',
            spectral_model='no_loss',
            temperature_model='sapm',
            losses_model='no_loss'
        )
        
        # 运行模型
        mc.run_model(weather_data)
        
        # 分析结果
        dc_power = mc.results.dc.fillna(0)
        ac_power = mc.results.ac.fillna(0)
        
        daily_dc_energy = dc_power.sum() / 1000  # kWh
        daily_ac_energy = ac_power.sum() / 1000  # kWh
        peak_dc_power = dc_power.max()
        peak_ac_power = ac_power.max()
        
        print(f"  理论DC容量: {config['pdc0'] * config['modules_per_string'] * config['strings']}W")
        print(f"  峰值DC功率: {peak_dc_power:.0f}W")
        print(f"  峰值AC功率: {peak_ac_power:.0f}W")
        print(f"  日DC发电量: {daily_dc_energy:.2f} kWh")
        print(f"  日AC发电量: {daily_ac_energy:.2f} kWh")
        print(f"  DC/AC效率: {daily_ac_energy/daily_dc_energy:.3f}")
        
        # 检查是否合理
        expected_dc_capacity = config['pdc0'] * config['modules_per_string'] * config['strings']
        if abs(peak_dc_power - expected_dc_capacity) / expected_dc_capacity < 0.1:
            print(f"  ✅ DC功率符合预期")
        else:
            print(f"  ❌ DC功率异常 (期望约{expected_dc_capacity}W)")

def test_simple_calculation():
    """简单的手工计算验证"""
    print(f"\n🧮 手工计算验证:")
    
    # 假设条件
    daily_irradiation = 5.0  # kWh/m²/day (北京夏季典型值)
    system_capacity = 10.0   # kW
    system_efficiency = 0.15  # 15%综合效率
    
    # 理论计算
    theoretical_generation = daily_irradiation * system_capacity * system_efficiency
    print(f"  日辐射量: {daily_irradiation} kWh/m²")
    print(f"  系统容量: {system_capacity} kW")
    print(f"  系统效率: {system_efficiency*100}%")
    print(f"  理论日发电量: {theoretical_generation:.2f} kWh")
    print(f"  理论年发电量: {theoretical_generation * 365:.0f} kWh")

if __name__ == "__main__":
    test_pvwatts_parameters()
    test_simple_calculation()
