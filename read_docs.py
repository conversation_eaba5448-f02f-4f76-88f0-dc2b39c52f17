#!/usr/bin/env python3
"""
读取项目中的Word文档内容
"""

import os
from docx import Document

def read_docx_content(file_path):
    """读取docx文件内容"""
    try:
        doc = Document(file_path)
        content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        return '\n'.join(content)
    except Exception as e:
        return f"读取文件失败: {e}"

def main():
    """主函数"""
    docx_files = [
        "光伏参谋-PRD.docx",
        "光伏参谋-Survey.docx", 
        "光伏参谋-UI交互.docx"
    ]
    
    for file_name in docx_files:
        if os.path.exists(file_name):
            print(f"\n{'='*50}")
            print(f"文档: {file_name}")
            print(f"{'='*50}")
            
            content = read_docx_content(file_name)
            print(content)
        else:
            print(f"文件不存在: {file_name}")

if __name__ == "__main__":
    main()
