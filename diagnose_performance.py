#!/usr/bin/env python3
"""
诊断光伏系统性能问题
"""

import asyncio
import pandas as pd
import numpy as np
from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams

async def diagnose_performance():
    """诊断性能问题"""
    print("🔍 开始诊断光伏系统性能问题...")
    
    # 获取数据
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    location = await geo_agent.parse_location("北京")
    solar_data = await solar_agent.get_solar_data(location)
    
    print(f"📊 基础数据分析:")
    
    # 1. 分析NASA原始数据
    total_irr = sum(data.irradiance for data in solar_data)
    avg_daily_irr = total_irr / len(solar_data)
    annual_irr = avg_daily_irr * 365
    
    print(f"  NASA年均日辐射: {avg_daily_irr:.2f} kWh/m²/day")
    print(f"  NASA年总辐射: {annual_irr:.0f} kWh/m²/year")
    
    # 2. 分析PVLib气象数据
    weather_df = pv_agent._prepare_weather_data(solar_data, location)
    
    ghi_annual = weather_df['ghi'].sum() / 1000  # 转换为kWh/m²
    ghi_daily_avg = ghi_annual / 365
    
    print(f"  PVLib年总GHI: {ghi_annual:.0f} kWh/m²/year")
    print(f"  PVLib日均GHI: {ghi_daily_avg:.2f} kWh/m²/day")
    print(f"  数据转换损失: {(1 - ghi_annual/annual_irr)*100:.1f}%")
    
    # 3. 分析PVLib计算结果
    system_params = PVSystemParams(capacity_kw=10.0)
    generation = await pv_agent.calculate_power_generation(location, solar_data, system_params)
    
    if generation:
        annual_gen = generation.annual_generation
        capacity_factor = annual_gen / (10 * 8760)  # 10kW系统
        
        print(f"\n⚡ PVLib计算结果:")
        print(f"  年发电量: {annual_gen:.0f} kWh")
        print(f"  容量因子: {capacity_factor:.3f} ({capacity_factor*100:.1f}%)")
        
        # 4. 理论计算对比
        # 理论发电量 = 年辐射量 × 系统容量 × 系统效率
        theoretical_gen = annual_irr * 10 * 0.15  # 假设15%综合效率
        
        print(f"\n📐 理论计算对比:")
        print(f"  理论发电量: {theoretical_gen:.0f} kWh (15%效率)")
        print(f"  实际/理论比: {annual_gen/theoretical_gen:.3f}")
        
        # 5. 行业标准对比
        print(f"\n🏭 行业标准对比:")
        print(f"  北京典型值: 12,000-15,000 kWh/年")
        print(f"  我们的结果: {annual_gen:.0f} kWh/年")
        print(f"  与行业标准比: {annual_gen/13500:.3f} (以13.5MWh为基准)")
        
        # 6. 详细参数分析
        print(f"\n🔧 系统参数分析:")
        print(f"  装机容量: {system_params.capacity_kw} kW")
        print(f"  倾角: {system_params.tilt_angle}°")
        print(f"  方位角: {system_params.azimuth}°")
        print(f"  组件效率: {system_params.module_efficiency}")
        print(f"  系统效率: {system_params.system_efficiency}")
        
        # 7. 月度效率分析
        print(f"\n📅 月度效率分析:")
        monthly_nasa = {}
        for data in solar_data:
            month = int(data.date[4:6])
            if month not in monthly_nasa:
                monthly_nasa[month] = []
            monthly_nasa[month].append(data.irradiance)
        
        for month in [1, 6, 12]:  # 分析关键月份
            if month in monthly_nasa:
                nasa_avg = sum(monthly_nasa[month]) / len(monthly_nasa[month])
                pv_gen = generation.monthly_generation[month-1]
                
                # 计算该月的理论发电量
                days_in_month = 31 if month in [1, 12] else 30
                monthly_theoretical = nasa_avg * days_in_month * 10 * 0.15
                
                efficiency = pv_gen / monthly_theoretical if monthly_theoretical > 0 else 0
                
                print(f"  {month}月: NASA {nasa_avg:.2f} → PV {pv_gen:.0f}kWh (效率: {efficiency:.3f})")
        
        # 8. 诊断建议
        print(f"\n💡 诊断建议:")
        
        if annual_gen < 8000:
            print("  ❌ 发电量严重偏低，可能问题:")
            print("    - PVLib参数配置过于保守")
            print("    - 辐射数据转换有损失")
            print("    - 系统效率设置过低")
        elif annual_gen < 12000:
            print("  ⚠️ 发电量偏低，建议优化:")
            print("    - 检查系统效率参数")
            print("    - 优化PVLib模型配置")
        else:
            print("  ✅ 发电量在合理范围内")
            
        # 9. 具体优化建议
        print(f"\n🎯 具体优化方向:")
        
        if capacity_factor < 0.10:
            print("  1. 容量因子过低，检查:")
            print("     - 逆变器效率设置")
            print("     - 温度损失系数")
            print("     - 系统损失参数")
            
        if ghi_annual < annual_irr * 0.9:
            print("  2. 辐射数据转换损失大，检查:")
            print("     - 小时级分配算法")
            print("     - 太阳位置计算精度")
            
        print(f"  3. 建议调整参数:")
        print(f"     - 逆变器效率: 0.96 → 0.98")
        print(f"     - 温度系数: -0.004 → -0.0035")
        print(f"     - 系统损失: 减少5-10%")

if __name__ == "__main__":
    asyncio.run(diagnose_performance())
