#!/usr/bin/env python3
"""
诊断1月发电量异常偏低的问题
分析PVLib计算的详细过程
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeoAgent
from agents.solar_agent import SolarAgent
from agents.pv_agent import PVAgent
from core.models import PVSystemParams
from core.config import settings


async def diagnose_january_issue():
    """诊断1月发电量问题"""
    
    print("🔍 诊断北京万橡悦府1月发电量异常问题")
    print("=" * 60)
    
    # 初始化代理
    geo_agent = GeoAgent()
    solar_agent = SolarAgent()
    pv_agent = PVAgent()
    
    # 解析地址
    location = await geo_agent.parse_location("北京市万橡悦府2期")
    solar_data = await solar_agent.get_solar_data(location)
    
    system_params = PVSystemParams(
        capacity_kw=10.0,
        tilt_angle=30.0,
        azimuth=180.0,
        module_efficiency=0.20,
        system_efficiency=0.85,
        performance_ratio=0.80
    )
    
    print(f"📊 分析{len(solar_data)}天的数据")
    
    # 手动创建天气数据以便检查
    print("\n🛰️ 检查原始太阳辐射数据...")
    
    # 检查1月和2月的原始数据
    january_data = []
    february_data = []
    
    for i, data_point in enumerate(solar_data):
        # 假设数据是按时间顺序排列的，计算日期
        # 从最近一年开始
        base_date = datetime.now() - timedelta(days=391-i)
        
        if base_date.month == 1:
            january_data.append(data_point.irradiance)
        elif base_date.month == 2:
            february_data.append(data_point.irradiance)
    
    print(f"1月原始太阳辐射数据:")
    print(f"  数据点数: {len(january_data)}")
    if january_data:
        print(f"  平均值: {np.mean(january_data):.3f} kWh/m²/day")
        print(f"  最小值: {np.min(january_data):.3f} kWh/m²/day")
        print(f"  最大值: {np.max(january_data):.3f} kWh/m²/day")
        
    print(f"\n2月原始太阳辐射数据:")
    print(f"  数据点数: {len(february_data)}")
    if february_data:
        print(f"  平均值: {np.mean(february_data):.3f} kWh/m²/day")
        print(f"  最小值: {np.min(february_data):.3f} kWh/m²/day")
        print(f"  最大值: {np.max(february_data):.3f} kWh/m²/day")
    
    # 现在进行完整的PVLib计算，但输出中间步骤
    print(f"\n⚡ 执行PVLib计算(带详细输出)...")
    
    # 调用PVAgent的内部方法进行详细分析
    weather_df = pv_agent._prepare_weather_data(solar_data)
    
    print(f"\n📅 检查准备后的天气数据:")
    print(f"  总数据点: {len(weather_df)}")
    print(f"  日期范围: {weather_df.index[0]} 到 {weather_df.index[-1]}")
    
    # 检查1月和2月的小时数据
    jan_weather = weather_df[weather_df.index.month == 1]
    feb_weather = weather_df[weather_df.index.month == 2]
    
    print(f"\n1月小时数据统计:")
    print(f"  数据点数: {len(jan_weather)}")
    if len(jan_weather) > 0:
        print(f"  GHI平均: {jan_weather['ghi'].mean():.2f} W/m²")
        print(f"  温度平均: {jan_weather['temp_air'].mean():.2f} °C")
        print(f"  日期范围: {jan_weather.index[0]} 到 {jan_weather.index[-1]}")
    
    print(f"\n2月小时数据统计:")
    print(f"  数据点数: {len(feb_weather)}")
    if len(feb_weather) > 0:
        print(f"  GHI平均: {feb_weather['ghi'].mean():.2f} W/m²")
        print(f"  温度平均: {feb_weather['temp_air'].mean():.2f} °C")
        print(f"  日期范围: {feb_weather.index[0]} 到 {feb_weather.index[-1]}")
    
    # 执行完整计算
    power_generation = await pv_agent.calculate_power_generation(
        location, solar_data, system_params
    )
    
    if power_generation:
        print(f"\n📈 最终计算结果:")
        monthly_data = power_generation.monthly_generation
        
        # 找出哪个索引对应1月和2月
        print(f"月度发电量数组: {monthly_data}")
        
        # 由于我们不知道确切的月份映射，显示所有月份
        for i, gen in enumerate(monthly_data):
            print(f"  月份 {i+1}: {gen:.0f} kWh")
    
    # 建议修复方案
    print(f"\n🔧 问题诊断与修复建议:")
    print("-" * 40)
    print("1. 检查日期索引是否正确映射到月份")
    print("2. 验证月度重采样逻辑('ME'而非'M')")
    print("3. 确认小时数据的时区处理")
    print("4. 检查是否有数据缺失导致某月数据不完整")


if __name__ == "__main__":
    asyncio.run(diagnose_january_issue())
